"""
认证相关API端点
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.auth import AuthManager, get_current_user, refresh_access_token
from ...core.database import get_db
from ...core.exceptions import (
    AuthenticationException,
    BusinessException,
    ValidationException,
)
from ...core.models import User

logger = structlog.get_logger()

router = APIRouter(prefix="/auth", tags=["认证"])


# 请求/响应模型
class UserRegisterRequest(BaseModel):
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=100, description="密码")


class UserLoginRequest(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class TokenResponse(BaseModel):
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class RefreshTokenRequest(BaseModel):
    refresh_token: str = Field(..., description="刷新令牌")


class UserInfoResponse(BaseModel):
    id: str = Field(..., description="用户ID")  # 改为str以支持UUID
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="用户邮箱")
    is_active: bool = Field(..., description="是否激活")
    is_first_time: bool = Field(..., description="是否首次登录")
    created_at: str = Field(..., description="创建时间")


class ChangePasswordRequest(BaseModel):
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")


@router.post(
    "/register",
    response_model=Dict[str, Any],
    summary="用户注册",
    status_code=status.HTTP_201_CREATED,
)
async def register(
    request: UserRegisterRequest, db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    用户注册

    - **username**: 用户名，3-50个字符
    - **password**: 密码，至少6个字符
    """
    try:
        # 检查用户名是否已存在
        query = select(User).where(User.username == request.username)
        result = await db.execute(query)
        existing_user = result.scalar_one_or_none()

        if existing_user:
            raise BusinessException(
                message="用户名已存在",
                code="USERNAME_EXISTS",
                details={"username": request.username},
            )

        # 创建新用户
        password_hash = AuthManager.hash_password(request.password)
        new_user = User(
            username=request.username,
            password_hash=password_hash,
            email=request.username + "@example.com",  # 临时邮箱，后续可以改进
        )

        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)

        logger.info(
            "User registered successfully",
            username=request.username,
            user_id=new_user.id,
        )

        return {
            "success": True,
            "message": "用户注册成功",
            "data": {"user_id": new_user.id, "username": new_user.username},
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error("Registration failed", username=request.username, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试",
        )


@router.post("/login", response_model=TokenResponse, summary="用户登录")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """
    用户登录

    - **username**: 用户名
    - **password**: 密码

    返回访问令牌和刷新令牌
    """
    try:
        # 认证用户
        user = await AuthManager.authenticate_user(
            form_data.username, form_data.password, db
        )

        if not user:
            raise AuthenticationException(
                message="用户名或密码错误", details={"username": form_data.username}
            )

        # 创建令牌
        token_data = {"sub": str(user.id), "username": user.username}
        access_token = AuthManager.create_access_token(data=token_data)
        refresh_token = AuthManager.create_refresh_token(data=token_data)

        logger.info(
            "User logged in successfully",
            username=user.username,
            user_id=user.id,
        )

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=30 * 60,  # 30分钟
        )

    except AuthenticationException:
        raise
    except Exception as e:
        logger.error("Login failed", username=form_data.username, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试",
        )


@router.post("/login/oauth", response_model=TokenResponse, summary="OAuth2密码流登录")
async def login_oauth(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """
    OAuth2密码流登录（兼容标准OAuth2客户端）
    """
    request = UserLoginRequest(username=form_data.username, password=form_data.password)
    return await login(request, db)


@router.post("/refresh", response_model=Dict[str, str], summary="刷新访问令牌")
async def refresh_token(
    request: RefreshTokenRequest, db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    使用刷新令牌获取新的访问令牌

    - **refresh_token**: 刷新令牌
    """
    try:
        result = await refresh_access_token(request.refresh_token, db)
        logger.info("Token refreshed successfully")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新失败，请稍后重试",
        )


@router.get("/me", response_model=UserInfoResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> UserInfoResponse:
    """
    获取当前登录用户的信息

    需要有效的访问令牌
    """
    return UserInfoResponse(
        id=str(current_user.id),  # 将UUID转换为字符串
        username=current_user.username,
        email=current_user.email,
        is_active=current_user.is_active,
        is_first_time=current_user.is_first_time,
        created_at=current_user.created_at.isoformat(),
    )


@router.post("/change-password", response_model=Dict[str, Any], summary="修改密码")
async def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    修改当前用户密码

    - **current_password**: 当前密码
    - **new_password**: 新密码，至少6个字符
    """
    try:
        # 验证当前密码
        if not AuthManager.verify_password(
            request.current_password, current_user.password_hash
        ):
            raise AuthenticationException(
                message="当前密码错误", details={"field": "current_password"}
            )

        # 检查新密码是否与当前密码相同
        if AuthManager.verify_password(
            request.new_password, current_user.password_hash
        ):
            raise ValidationException(message="新密码不能与当前密码相同", field="new_password")

        # 更新密码
        new_password_hash = AuthManager.hash_password(request.new_password)
        current_user.password_hash = new_password_hash

        await db.commit()

        logger.info(
            "Password changed successfully",
            user_id=current_user.id,
            username=current_user.username,
        )

        return {"success": True, "message": "密码修改成功"}

    except (AuthenticationException, ValidationException):
        raise
    except Exception as e:
        logger.error("Password change failed", user_id=current_user.id, error=str(e))
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败，请稍后重试",
        )


@router.post("/logout", response_model=Dict[str, Any], summary="用户登出")
async def logout(
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    用户登出

    注意：由于JWT是无状态的，实际的令牌失效需要客户端删除令牌
    这个端点主要用于记录登出事件和清理服务端状态（如果有的话）
    """
    logger.info(
        "User logged out",
        user_id=current_user.id,
        username=current_user.username,
    )

    return {"success": True, "message": "登出成功"}


@router.get("/verify", response_model=Dict[str, Any], summary="验证令牌")
async def verify_token(
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    验证访问令牌是否有效

    如果令牌有效，返回用户基本信息
    """
    return {
        "success": True,
        "message": "令牌有效",
        "data": {
            "user_id": current_user.id,
            "username": current_user.username,
        },
    }
