# AI Agent 驱动的加密货币智能跟单系统 - 后端设计文档

## 1. 愿景与原则

### 1.1 项目愿景

本项目旨在构建一个基于**自主 AI 智能体 (Autonomous AI Agent)** 的高级自动化交易系统。核心目标是解决在社交平台上手动跟单时面临的**高延迟、操作繁琐、无法全天候跟踪**以及**指令语言非标准化**的核心痛点。

**核心能力**:

- **实时监控**：7x24 小时不间断监听 Discord 等信号源。
- **深度理解**：准确解析模糊、复杂、含指代的自然语言交易指令，并能处理中英混合输入。
- **智能规划**：基于上下文和风控规则生成最优执行计划。
- **严格风控**：在交易的每一个环节执行多层次、不可绕过的风险控制，确保资金安全。
- **自主执行**：全自动化交易执行，具备幂等性，并能在错误发生时尝试自我修正。
- **自适应学习**：为未来从历史交易中学习、持续优化决策质量奠定基础（通过结构化的日志和检查点）。

最终愿景是交付一个能够实时监控、理解、规划、风控并自主执行自然语言交易指令的**下一代交易智能体**，并赋予其通过与环境交互实现**自适应学习和演化的能力**。

### 1.2 设计原则

为平衡个人开发的敏捷性与项目的长期扩展性，我们遵循以下核心设计原则：

- **单一应用，模块化设计 (Monolith First, Modular Design)**：以单个 FastAPI 应用作为起点，内部逻辑高度模块化（API 层、业务逻辑层、数据访问层职责分明），为未来可能的微服务化拆分预留接口。这对于单人开发和初期部署最为高效。
- **状态驱动与可追溯 (State-Driven & Traceable)**：系统的核心是围绕一个明确的状态机（由 LangGraph 实现）构建的。每一次状态的变迁都必须被记录，确保每个决策路径都是完全透明、可审计、可恢复、可调试的。
- **契约优先 (Contract-First)**：所有内部模块间、前后端间以及与外部系统的交互，都必须有严格的、基于 Pydantic 模型的模式（Schema）定义。这对于 AI 理解和生成类型安全的代码至关重要。
- **务实的数据持久化 (Pragmatic Persistence)**：选择功能强大且成熟的 PostgreSQL 作为**唯一的**核心数据存储，利用其 JSONB 字段的灵活性来存储半结构化的 AI 日志和上下文数据，以简化 MVP 阶段的架构。

## 2. 系统架构

### 2.1 架构图 (C4 - 容器图)

系统被设计为一个“结构化单体”应用，辅以一个独立的前端应用。这种架构极大地降低了个人开发和部署的复杂性，是个人项目的最佳实践。**初期版本将不依赖 Redis**，以实现最简化的部署。

```mermaid
graph TD
    subgraph "外部系统 (External Systems)"
        direction LR
        ext_discord["Discord API"]
        ext_exchange["CCXT 统一交易所 API"]
        ext_llm["LLM API (OpenAI, Anthropic, etc.)"]
    end

    actor_user["用户 (User)"]

    subgraph "智能跟单系统 (System Boundary - 单机部署)"
        direction TB

        frontend["<b>前端应用 (Frontend)</b><br/>(Vue.js 3 + Vuetify 3)<br/>提供用户交互界面<br/>通过 HTTPS/WSS 与后端通信"]

        subgraph "后端单体应用 (Backend Monolith - FastAPI)"
            direction LR

            api_gateway["<b>API & WebSocket 层</b><br/>(FastAPI)<br/>处理 HTTP 请求，管理 WebSocket 连接 (内存中)"]

            subgraph "AI 核心 (AI Core)"
                agent_core["<b>Agent Core (LangGraph)</b><br/><i>系统的智能决策中枢</i><br/>执行任务规划、工具调用、自我修正"]
            end

            subgraph "后台任务 (Background Tasks - asyncio)"
                discord_listener["<b>Discord 监听器</b><br/>(discord.py-self)<br/>智能监听指定频道的交易信号<br/>支持消息去重、信号识别、自动重连"]
                price_watcher["<b>价格观察者</b><br/>(asyncio.Task)<br/>监控条件订单的触发条件"]
            end
        end

        subgraph "数据存储 (Data Persistence)"
            db_postgres["<b>PostgreSQL 数据库</b><br/>(Alembic 管理)<br/>存储所有核心业务数据<br/><b>并包含 Agent 状态检查点</b>"]
        end
    end

    %% --- 交互流程 ---
    user -- "HTTPS/WSS" --> frontend
    frontend -- "REST API / WebSocket" --> api_gateway

    discord_listener -- "1. 接收消息, 创建任务" --> agent_core
    price_watcher -- "触发条件, 创建任务" --> agent_core
    api_gateway -- "用户操作, 创建任务" --> agent_core

    agent_core -- "调用 LLM 进行推理" --> ext_llm
    agent_core -- "调用工具执行交易" --> ext_exchange
    agent_core -- "读/写业务数据" --> db_postgres
    agent_core -- "读/写 Agent 状态检查点" --> db_postgres

    db_postgres -- "状态变更 (e.g., via Triggers)" --> api_gateway
    api_gateway -- "实时推送更新 (WebSocket)" --> frontend

    style agent_core fill:#e6f2ff,stroke:#333,stroke-width:2px
```

- **关于 Redis 的说明**: 为简化初期版本的部署和维护，本项目**不将 Redis 作为必要依赖**。LangGraph 的状态检查点将持久化到 PostgreSQL 数据库中。WebSocket 通信将在单个 FastAPI 实例的内存中处理。未来，当系统需要横向扩展以支持更多用户时，可以引入 Redis 作为性能缓存、WebSocket 消息总线和更高效的检查点存储，但这将作为第二阶段的优化。

## 3. AI 核心设计 (AI Core Design)

### 3.1 Agent 框架选型：LangGraph

我们选择 **LangGraph**，因为它能将 Agent 的工作流定义为一个**有向图 (Graph)**，提供了**确定性、可追溯性、内置状态管理和强大的循环/自我修正能力**，完美契合交易场景对高可靠性的要求。

### 3.1.1 错误处理和可观测性架构

为确保系统的高可靠性和可维护性，我们实现了完整的错误处理和可观测性架构：

#### 分层异常处理系统

```python
# 异常类层次结构
class AgentError(Exception):
    """Agent 执行过程中的基础异常类"""
    - message: str - 错误消息
    - error_code: str - 错误代码
    - node_name: Optional[str] - 发生错误的节点名称
    - recoverable: bool - 是否可恢复
    - timestamp: datetime - 错误发生时间

class AgentTimeoutError(AgentError):
    """Agent 节点执行超时异常"""
    - timeout_seconds: float - 超时时长

class AgentNetworkError(AgentError):
    """Agent 网络相关异常"""
    - retry_count: int - 重试次数
    - last_error: Exception - 最后一次错误

class AgentValidationError(AgentError):
    """Agent 数据验证异常"""
    - field_name: str - 验证失败的字段
    - field_value: Any - 字段值
```

#### 统一错误处理装饰器

所有Agent节点函数都通过统一的错误处理装饰器进行包装：

```python
@with_error_handling(
    node_name="parse_intents",
    timeout_seconds=30.0,
    retry_count=2,
    retry_delay=2.0
)
async def parse_intents(state: AgentState, db: AsyncSession) -> AgentState:
    # 节点实现
    pass
```

**装饰器功能**：
- **超时控制**：防止节点执行时间过长
- **重试机制**：对可恢复错误进行自动重试
- **结构化日志**：自动记录节点执行的开始、结束和错误信息
- **状态保护**：确保异常不会破坏Agent状态的完整性
- **错误分类**：自动识别错误类型并采取相应处理策略

#### 结构化日志系统

使用 `structlog` 实现结构化日志记录：

```python
# 日志配置
logger = structlog.get_logger()

# 日志记录示例
logger.info("Parsing intents",
           task_id=str(state.task_id),
           user_id=state.user_id,
           input_length=len(state.raw_input))

logger.error("Intent parsing failed",
            task_id=str(state.task_id),
            error=str(e),
            retry_count=state.retry_count)
```

**日志字段标准**：
- `task_id`: 任务唯一标识符
- `user_id`: 用户ID
- `node_name`: 当前执行节点
- `timestamp`: 时间戳
- `level`: 日志级别
- `message`: 日志消息
- `context`: 上下文信息（如错误详情、性能指标等）

#### 性能监控和指标

**节点执行指标**：
- 执行时间统计
- 成功/失败率
- 重试次数分布
- 超时频率

**系统级指标**：
- 并发任务数量
- 内存使用情况
- 数据库连接池状态
- WebSocket连接数量

**业务指标**：
- 交易成功率
- 平均处理延迟
- 用户确认响应时间
- 风控拒绝率

### 3.2 Agent 核心业务流程图 (状态机)

整个交易处理流程被建模为如下的 LangGraph 状态图。图的状态由 `AgentState` Pydantic 模型定义（见附录 A）。此图是系统的核心，明确了所有决策路径。

```mermaid
stateDiagram-v2
    direction LR

    state "A. 文本预处理" as Preprocess
    state "B. 解析指令" as Parse
    state "C. 获取上下文" as Context
    state "D. 制定初步计划" as Plan
    state "E. 风险评估" as Risk
    state "F. 执行计划" as Execute
    state "I. 分析错误" as AnalyzeError
    state "J. 请求用户确认" as UserConfirm
    state "任务成功" as Success
    state "任务失败" as Failure

    [*] --> Preprocess
    Preprocess --> Parse: 文本已规范化

    Parse --> Context: 指令清晰 (is_clear)
    Parse --> UserConfirm: 指令模糊 (is_ambiguous)
    Parse --> Failure: 解析失败 (is_invalid)

    UserConfirm --> Context: 用户批准 (approved)
    UserConfirm --> Failure: 用户拒绝/超时 (rejected_or_timed_out)

    Context --> Plan: 上下文已加载
    Plan --> Risk: 已生成执行计划 (execution_plan)

    Risk --> Execute: 风控通过 (approved)
    Risk --> UserConfirm: 风控警告 (warning)
    Risk --> Failure: 风控拒绝 (rejected)

    Execute --> Success: 全部成功 (all_steps_succeed)
    Execute --> AnalyzeError: 出现API错误 (api_error)

    AnalyzeError --> Execute: 错误可修正 (is_correctable)
    AnalyzeError --> Failure: 错误不可修正 (is_fatal)

    note right of Parse
        使用LLM解析自然语言
        输出结构化交易意图
        判断交易方向和置信度
    end note

    note right of UserConfirm
        通过WebSocket请求用户确认
        使用LangGraph检查点机制暂停/恢复
        用户可直接批准、拒绝或<b>修正计划</b>
    end note

    note right of Risk
        检查交易对白名单
        验证订单规模限制
        评估总风险暴露
    end note
```

### 3.3 核心节点逻辑详解

#### 3.3.1 节点实现架构

**异步包装与错误处理机制**。所有节点函数都通过统一的异步包装器进行封装，确保：

- **异步执行**: 支持高并发的交易处理
- **统一错误处理**: 捕获并记录所有异常，防止状态图崩溃
- **状态日志**: 自动记录节点执行的开始、结束和错误信息
- **超时控制**: 防止节点执行时间过长

#### 3.3.2 节点 A (文本预处理 - Preprocess)

- **输入**: 原始消息字符串。
- **处理**: 在将文本送入 LLM 解析前，执行一系列规范化操作以提高解析的稳定性和准确性。包括：
  - 转换为小写。
  - 移除不相关的 emoji 或特殊字符。
  - 标准化常见的交易俚语 (e.g., "梭哈" -> "100%仓位", "tp" -> "take profit")。
- **实现细节**: 使用正则表达式和预定义的字典进行文本清理和替换。
- **输出**: 更新 `state.raw_input` 为规范化后的文本。

#### 3.3.3 节点 B (解析指令 - Parse)

- **输入**: `state.raw_input` (经过预处理的文本)
- **功能**: 调用 LLM（使用 `Instructor` 强制返回 `List[ParsedIntent]`），将自然语言转换为结构化的意图列表。**LLM 在此步骤中直接判断交易方向 `side`。Prompt 策略详见附录 C**。
- **核心特性**:
  - **方向判断**: LLM 在此步骤中直接判断交易方向 `side` (`buy`/`sell`)。
  - **意图分类**: 能够识别`CREATE_ORDER`, `CLOSE_ORDER`, `MODIFY_ORDER`以及`PARTIAL_CLOSE_ORDER` (部分平仓) 等多种意图。
  - **置信度评估**: 为每个解析结果提供置信度分数。
  - **增强的 Prompt 工程**：为提升复杂指令的解析准确率，此节点将采用“思维链 (Chain-of-Thought)” Prompt 策略。即要求 LLM 在输出最终的结构化 JSON 之前，先进行一步一步的分析（例如：“第一步：识别关键词... 第二步：判断交易方向... 第三步：提取参数...”），并将此分析过程作为推理的一部分。具体实现见附录 C。
- **输出**: 更新 `state.parsed_intents`。
- **路由逻辑**: 遍历 `parsed_intents` 列表，基于置信度实现分层智能决策：
  1. **自动执行路径**: 如果 `intent.confidence >= risk_configs.auto_approve_threshold`（默认0.9），且 `intent.intent_type` 不是 `AMBIGUOUS`，且 `intent.side` 不为 `None`，则直接路由到**节点 C (获取上下文)**，启动自动执行流程。
  2. **人机协作路径**: 如果 `intent.confidence >= risk_configs.confidence_threshold`（默认0.8）但低于 `auto_approve_threshold`，则路由到**节点 J (请求用户确认)**，进行人机协作决策。
  3. **强制确认路径**: 如果任何一个 `intent` 满足以下任一条件，则强制路由到**节点 J (请求用户确认)**：
     - `intent.intent_type` 是 `AMBIGUOUS`
     - 对于交易类意图，`intent.side` 为 `None` (null)
     - `intent.confidence` 低于用户预设的 `confidence_threshold`
  4. **拒绝路径**: 如果置信度过低（< 0.5），则直接终止并返回错误。

#### 3.3.4 节点 C (获取上下文 - Context)

- **输入**: `state.parsed_intents`
- **功能**: 收集执行计划所需的环境信息。
- **处理**: Agent 进行一系列密集的工具调用，以全面构建执行计划所需的环境信息。
  1. 从数据库加载并填充 `state.context['risk_config']`。
  2. 调用 `get_active_orders` 工具，填充 `state.context['active_orders']`。
  3. 根据解析出的交易对，调用 `get_market_price` 工具，填充 `state.context['market_prices']`。
  4. 如果意图涉及指代，调用 `get_message_history` 工具，填充 `state.context['message_history']`。
- **输出**: 一个被完整填充的 `state.context` 字典。

#### 3.3.5 节点 D (制定初步计划 - Plan)

- **输入**: `state.parsed_intents`, `state.context`
- **功能**: 基于解析的意图和上下文生成具体的、可执行的`TradePlan`对象列表。
- **核心逻辑**:
  - **意图转换**: 将每个 `ParsedIntent` 转换为 `TradePlan`。
  - **参数补全**: 使用上下文信息（如当前市价、用户默认配置）补全缺失参数。
  - **数量计算**: 根据风控规则和用户输入（包括绝对金额或**平仓百分比**）计算最终的交易数量。
- **平仓方向的确定性**: 对于 `CLOSE_ORDER` 意图，交易计划的 `side` **必须**根据待平仓订单的持仓方向严格取反（`buy` -> `sell`, `sell` -> `buy`），**绝不能**依赖 AI 的判断。这是防止 AI 幻觉导致反向开仓风险的核心安全机制。
- **输出**: 更新 `state.execution_plan`。

#### 3.3.6 节点 E (风险评估 - Risk)

- **功能**: 对生成的执行计划进行多维度、不可绕过的风险检查。
- **风险检查清单**:
  1. **交易对白名单检查**: `plan.symbol` 是否在 `risk_configs.allowed_symbols` 列表内？
  2. **单笔订单规模检查**: `plan.quantity_usd` 是否小于 `risk_configs.max_position_size_usd`？
  3. **并发订单数量检查**: 当前活跃订单数是否小于 `risk_configs.max_concurrent_orders`？
  4. **总风险暴露检查**: (当前所有活跃订单的总价值 + `plan.quantity_usd`) 是否小于 `risk_configs.max_total_position_value_usd`？
- **路由逻辑**: 任何一项检查失败，工作流都将立即终止并进入“任务失败”状态。

> 在执行任何交易计划前，**必须**在**节点 E (风险评估)** 中，按顺序通过所有检查。所有参数均从与用户关联的 `risk_configs` 表中读取。任何一项检查失败，工作流都将立即终止并进入“任务失败”状态，同时通过 WebSocket 向用户发送明确的“风控拒绝”通知。

#### 3.3.7 节点 F (执行计划 - Execute)

- **输入**: `state.execution_plan`
- **功能**: 执行交易计划，与交易所进行实际交互。
- **执行策略**: 采用 **“尽力而为，完整记录” (Best-effort with logging)** 的策略。
  1. 按顺序迭代 `execution_plan` 中的每一个 `TradePlan`，调用 `execute_trade` 工具。
  2. 无论成功或失败，都将返回的 `TradeResult` 记录下来。
  3. **即使中途某个 plan 执行失败，也会继续尝试执行后续的 plan**。
- **幂等性设计**：作为金融系统的核心安全要求，此节点在调用`execute_trade`工具前，必须执行幂等性检查。系统会使用一个唯一的标识符（如`source_message_id`或关联的`task_id`）来生成`client_order_id`。在下单前，会查询数据库`ORDERS`表，检查具有相同`client_order_id`的订单是否已存在或正在处理。如果存在，则跳过执行，直接返回已有结果，防止因任何原因（如网络重试、消息队列重投）导致的重复交易。
- **路由逻辑**: 所有 plan 尝试完毕后，如果存在任何失败，则进入 `AnalyzeError` 节点；如果全部成功，则进入 `Success` 节点。

#### 3.3.8 节点 I (分析错误与自我修正 - AnalyzeError)

- **输入**: `state.error_message`, `state.retry_count`
- **功能**: 分析执行错误，判断是否可以重试。
- **处理**: 对交易所返回的 API 错误进行分类。调用 LLM 分析错误信息，判断其性质。**Prompt 策略详见附录 C**。
- **路由逻辑**: 如果 LLM 判断错误是可修正的（如瞬时网络问题、API 超时）且重试次数未超限，则增加 `state.retry_count` 并路由回**节点 F**；否则（如资金不足、交易对无效），路由到**任务失败**。

#### 3.3.9 节点 J (请求用户确认 - UserConfirm)

- **功能**: 当 AI 无法确定用户意图时，请求用户澄清，并暂停工作流。
- **核心特性**:
  1. 在`PENDING_ACTIONS`表中创建一条记录，内容包含 AI 的提问和建议方案。
  2. **调用 LangGraph 的检查点机制，将当前 `AgentState` 的快照序列化后存入 PostgreSQL 的 `AGENT_CHECKPOINTS` 表中，然后暂停图的执行**。
  3. 通过 WebSocket 向前端推送`PENDING_ACTION_REQUIRED`事件，附带 `action_id` 和完整的待办详情。
  4. **交互式澄清与恢复**：用户可通过前端界面响应此待办事项。响应不仅仅是批准/拒绝，还可以是**修正后的交易计划 (`edited_plan`)**。当后端 API 接收到响应后，会从检查点恢复工作流，将用户的响应（包括可能的`edited_plan`）注入`AgentState`。如果`edited_plan`存在，它将覆盖原有的`execution_plan`，然后工作流继续执行。
- **恢复**:
  1. 用户通过前端界面的 API (`POST /api/v1/actions/{action_id}/respond`) 做出响应。
  2. API 后端将 `PENDING_ACTIONS` 表中的记录状态更新为 `APPROVED` 或 `REJECTED`，**并将用户完整的响应内容（包括可能的 `edited_plan`）存入 `response` 字段，以供审计**。
  3. API 后端根据 `task_id` 从 `AGENT_CHECKPOINTS` 表中加载检查点，将用户的响应注入 `state.user_response`，然后**调用 LangGraph 的 `resume` 方法，从暂停点继续执行图**。

### 3.4 工具集 (Tools)

Agent 可调用的工具是严格类型注解的 Python 函数。

- `get_active_orders(user_id: int) -> List[Order]`
- `find_orders_by_criteria(user_id: int, criteria: str) -> List[Order]`: **实现方式**：此工具将采用**严格基于规则的**方法进行实现，以保证性能和确定性，**严禁调用 LLM 进行二次解析**。它会包含一系列 `if/elif/else` 逻辑来解析常见的自然语言标准。
  - **示例**:
    - `criteria` 包含 "盈利" 或 "profitable" -> 筛选 `pnl > 0` 的订单。
    - `criteria` 包含 "亏损" 或 "loss" -> 筛选 `pnl < 0` 的订单。
    - `criteria` 包含 "最新" 或 "latest" -> 按 `created_at` 降序排序后取第一个。
    - `criteria` 包含 "BTC" 或 "eth" -> 筛选 `symbol` 包含对应币种的订单。
- `get_market_price(symbol: str) -> float`
- `execute_trade(trade_plan: TradePlan) -> TradeResult`
- `create_conditional_order(condition: ConditionalOrderSchema) -> ConditionalOrderResult`: **实现**：此工具仅在数据库的 `CONDITIONAL_ORDERS` 表中创建一条记录，状态为 `PENDING`。它**不执行**任何交易。
- `get_message_history(channel_id: str, limit: int = 10) -> List[str]`: 用于解决指代问题。

### 3.5 检查点存储与恢复机制

- **检查点存储策略**:
  - 每个图节点执行前后都会创建状态快照。
  - 关键决策点（如用户确认、任务结束）会强制将完整的 `AgentState` 序列化为 **JSON 格式**并持久化到 PostgreSQL 的 `AGENT_CHECKPOINTS` 表的 **`JSONB` 类型字段**中。
  - **采用 JSONB 格式的好处**：检查点数据是人类可读的，极大地便利了调试和问题排查。同时，PostgreSQL 对 JSONB 提供了强大的原生查询和索引能力，便于未来对历史状态数据进行深度分析和挖掘，而无需进行反序列化。
- **恢复流程**:
  1. 系统根据 `thread_id` (即 `task_id`) 从 `AGENT_CHECKPOINTS` 表加载最新检查点。
  2. 直接从 `JSONB` 字段反序列化 `AgentState` 对象。
  3. 根据当前状态确定下一个执行节点，恢复执行。

## 4. 数据库设计

### 4.1 实体关系图 (ERD)

采用 PostgreSQL 作为唯一核心数据库。Schema 的演进将由 **Alembic** 进行管理。

```mermaid
erDiagram
    USERS {
        uuid id PK
        string username UK "VARCHAR(50), 最少3字符"
        string email UK "VARCHAR(100), 默认空字符串"
        string password_hash "VARCHAR(255), 最少60字符"
        boolean is_active "默认TRUE"
        boolean is_first_time "默认TRUE"
        datetime created_at
        datetime updated_at
    }

    EXCHANGE_CONFIGS {
        uuid id PK
        int user_id FK
        string exchange_name "VARCHAR(50), 限制为支持的交易所"
        string encrypted_api_key "TEXT, 非空"
        string encrypted_api_secret "TEXT, 非空"
        string encrypted_passphrase "TEXT, 可选"
        boolean sandbox_mode "默认TRUE"
        boolean is_active "默认TRUE"
        datetime created_at
        datetime updated_at
    }

    RISK_CONFIGS {
        int id PK
        int user_id FK "UNIQUE约束"
        int max_concurrent_orders "1-100"
        decimal max_total_position_value_usd "NUMERIC(15,2)"
        decimal default_position_size_usd "NUMERIC(15,2)"
        decimal max_position_size_usd "NUMERIC(15,2)"
        decimal max_daily_loss_usd "NUMERIC(15,2)"
        decimal max_drawdown_percent "NUMERIC(5,2), 0-100"
        decimal stop_loss_percent "NUMERIC(5,2), 0-100"
        json allowed_symbols "ARRAY"
        string trading_hours_start "HH:MM格式"
        string trading_hours_end "HH:MM格式"
        decimal confidence_threshold "NUMERIC(3,2), 0-1"
        decimal auto_approve_threshold "NUMERIC(3,2), 0-1"
        decimal max_symbol_concentration_percent "NUMERIC(5,2), 0-100"
        boolean correlation_check_enabled "默认TRUE"
        boolean volatility_check_enabled "默认TRUE"
        datetime created_at
        datetime updated_at
    }

    ORDERS {
        uuid id PK
        int user_id FK
        string client_order_id UK "VARCHAR(100), 非空唯一"
        string exchange_order_id "VARCHAR(100), 可选"
        string source_message_id "VARCHAR(100), 可选"
        string symbol "VARCHAR(20), 非空"
        string side "VARCHAR(10), buy/sell"
        decimal quantity "NUMERIC(20,8), 大于0"
        decimal entry_price "NUMERIC(20,8), 可选且大于0"
        decimal close_price "NUMERIC(20,8), 可选且大于0"
        decimal pnl "NUMERIC(20,8), 盈亏"
        string status "VARCHAR(20), active/closed/failed/cancelled"
        jsonb agent_log "JSONB, 存储Agent执行日志"
        datetime created_at
        datetime closed_at "可选, 必须>=created_at"
    }

    CONDITIONAL_ORDERS {
        uuid id PK
        int user_id FK
        string symbol "VARCHAR(20)"
        jsonb trigger_condition "JSONB, 触发条件"
        jsonb action_plan "JSONB, 执行计划"
        string status "VARCHAR(20), PENDING/TRIGGERED/CANCELLED/EXPIRED"
        datetime created_at
        datetime updated_at
        datetime triggered_at "可选, 必须>=created_at"
    }

    PENDING_ACTIONS {
        uuid id PK
        string task_id "VARCHAR(100), 任务ID"
        int user_id FK
        string action_type "VARCHAR(50), USER_CONFIRMATION/RISK_OVERRIDE/MANUAL_INTERVENTION"
        jsonb details "JSONB, 动作详情"
        string status "VARCHAR(20), PENDING/APPROVED/REJECTED/EXPIRED"
        jsonb response "JSONB, 用户响应"
        datetime created_at
        datetime expires_at "必须>created_at"
        datetime resolved_at "可选, 必须>=created_at"
    }

    %% 信号表设计详见：信号功能模块设计文档.md 和 信号功能AI解析字段改进方案.md
    SIGNALS {
        uuid id PK
        int user_id FK
        string platform "VARCHAR(20), discord/telegram/manual"
        string platform_message_id "VARCHAR(255), 可选"
        string channel_id "VARCHAR(255), 可选"
        string channel_name "VARCHAR(255), 可选"
        string author_id "VARCHAR(255), 可选"
        string author_name "VARCHAR(255), 可选"
        text content "非空, 处理后的消息内容"
        text raw_content "可选, 原始消息内容"
        string message_type "VARCHAR(50), text/embed/attachment/reply"
        jsonb metadata "JSONB, 平台特定元数据"
        decimal confidence "NUMERIC(3,2), AI解析置信度"
        string ai_parse_status "VARCHAR(20), pending/success/failed/partial"
        string message_type_ai "VARCHAR(30), AI识别的消息类型"
        string llm_service "VARCHAR(20), 使用的LLM服务"
        boolean is_processed "默认FALSE"
        datetime processed_at "可选"
        datetime created_at
        datetime updated_at
    }

    AGENT_CHECKPOINTS {
        uuid id PK
        uuid task_id "任务ID"
        int user_id FK
        string node_name "VARCHAR(50), 节点名称"
        jsonb state_data "JSONB, Agent状态数据"
        datetime created_at
    }

    USERS ||--o{ EXCHANGE_CONFIGS : "has"
    USERS ||--|| RISK_CONFIGS : "has"
    USERS ||--o{ ORDERS : "creates"
    USERS ||--o{ SIGNALS : "receives"
    USERS ||--o{ CONDITIONAL_ORDERS : "creates"
    USERS ||--o{ PENDING_ACTIONS : "has"
    USERS ||--o{ AGENT_CHECKPOINTS : "belongs_to"
```

### 4.2 数据安全

- **凭证加密**: `EXCHANGE_CONFIGS` 表中的 API Key 和 Secret **必须**在应用层使用 `cryptography` 库的 Fernet 对称加密后才能存入数据库。
- **加密密钥管理**: Fernet 加密所用的主密钥**必须**通过**环境变量 (`APP_SECRET_KEY`)** 提供给应用，绝不能硬编码在代码或 Docker 镜像中。

### 4.3 数据库优化策略

- **索引优化**:
  - **用户表**: `idx_users_username`, `idx_users_created_at`
  - **订单表**: `idx_orders_user_status`, `idx_orders_user_symbol`, `idx_orders_user_created`, `idx_orders_status_created`, `idx_orders_symbol_created`
  - **信号表**: `idx_signals_user_id`, `idx_signals_platform`, `idx_signals_channel_id`, `idx_signals_created_at`, `idx_signals_is_processed`, `idx_signals_confidence`, `idx_signals_user_platform`, `idx_signals_user_created`, `idx_signals_ai_parse_status`, `idx_signals_message_type_ai`, `idx_signals_llm_service`, `idx_signals_metadata_gin`
  - **条件订单表**: `idx_conditional_orders_user_status`, `idx_conditional_orders_user_symbol`, `idx_conditional_orders_status_created`, `idx_conditional_orders_symbol_status`
  - **待处理动作表**: `idx_pending_actions_user_status`, `idx_pending_actions_task_id`, `idx_pending_actions_expires_at`, `idx_pending_actions_created_at`
  - **Agent检查点表**: `idx_agent_checkpoints_task_user`, `idx_agent_checkpoints_user_created`, `idx_agent_checkpoints_task_created`, `idx_agent_checkpoints_node_created`
  - **交易所配置表**: `idx_exchange_configs_user_id`, `idx_exchange_configs_user_active`, `idx_exchange_configs_exchange_active`
  - **风控配置表**: `idx_risk_configs_user_id`, `idx_risk_configs_updated_at`

- **GIN索引 (JSONB字段)**:
  - `idx_orders_agent_log_gin`: 订单Agent日志查询
  - `idx_conditional_orders_trigger_gin`: 条件订单触发条件查询
  - `idx_conditional_orders_action_gin`: 条件订单执行计划查询
  - `idx_pending_actions_details_gin`: 待处理动作详情查询
  - `idx_pending_actions_response_gin`: 待处理动作响应查询
  - `idx_agent_checkpoints_state_gin`: Agent状态数据查询

- **约束优化**:
  - **数据完整性约束**: 所有表都有适当的CHECK约束确保数据有效性
  - **外键约束**: 确保引用完整性
  - **唯一约束**: 防止重复数据（如client_order_id, username等）

- **分区策略 (适用于大量历史数据)**:
  - 可考虑对 `ORDERS` 表按 `created_at` 进行范围分区（如按月分区），将冷热数据分离，提高查询效率。

### 4.4 核心表结构定义 (SQL DDL)

#### 4.4.1 用户表 (USERS)

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_username ON users(username);
```

#### 4.4.2 订单表 (ORDERS)

```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(id),
    client_order_id VARCHAR(100) UNIQUE NOT NULL,
    exchange_order_id VARCHAR(100),
    source_message_id VARCHAR(100),
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL CHECK (side IN ('buy', 'sell')),
    quantity DECIMAL(20, 8) NOT NULL CHECK (quantity > 0),
    entry_price DECIMAL(20, 8),
    close_price DECIMAL(20, 8),
    pnl DECIMAL(20, 8),
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'closed', 'failed', 'cancelled')),
    agent_log JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP
);

CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_symbol ON orders(symbol);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

#### 4.4.3 风控配置表 (RISK_CONFIGS)

```sql
CREATE TABLE risk_configs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE NOT NULL REFERENCES users(id),
    max_concurrent_orders INTEGER NOT NULL DEFAULT 5 CHECK (max_concurrent_orders > 0),
    max_total_position_value_usd DECIMAL(15, 2) NOT NULL DEFAULT 1000.00 CHECK (max_total_position_value_usd > 0),
    default_position_size_usd DECIMAL(15, 2) NOT NULL DEFAULT 100.00 CHECK (default_position_size_usd > 0),
    max_position_size_usd DECIMAL(15, 2) NOT NULL DEFAULT 500.00 CHECK (max_position_size_usd > 0),
    allowed_symbols JSONB NOT NULL DEFAULT '["BTC/USDT", "ETH/USDT"]',
    confidence_threshold DECIMAL(3, 2) NOT NULL DEFAULT 0.80 CHECK (confidence_threshold BETWEEN 0 AND 1),
    auto_approve_threshold DECIMAL(3, 2) NOT NULL DEFAULT 0.95 CHECK (auto_approve_threshold BETWEEN 0 AND 1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_threshold_order CHECK (confidence_threshold <= auto_approve_threshold)
);

CREATE INDEX idx_risk_configs_user_id ON risk_configs(user_id);
```

**风控配置表字段说明：**

- `confidence_threshold`：置信度阈值（默认0.80），当LLM解析意图的置信度低于此值时，强制要求用户确认
- `auto_approve_threshold`：自动批准阈值（默认0.95），当置信度高于此值时，系统自动执行交易
- 分层决策逻辑：
  - `confidence >= auto_approve_threshold`：自动执行（SG-08智能信号自动执行）
  - `confidence_threshold <= confidence < auto_approve_threshold`：人机协作（SG-09智能信号人机协作执行）
  - `confidence < confidence_threshold`：强制用户确认或拒绝
- 约束条件：`confidence_threshold <= auto_approve_threshold`，确保阈值设置的逻辑合理性

#### 4.4.4 Agent 检查点表 (AGENT_CHECKPOINTS)

```sql
CREATE TABLE agent_checkpoints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL,
    user_id INTEGER NOT NULL REFERENCES users(id),
    node_name VARCHAR(50) NOT NULL,
    state_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_agent_checkpoints_task_id ON agent_checkpoints(task_id);
CREATE INDEX idx_agent_checkpoints_user_id ON agent_checkpoints(user_id);
CREATE INDEX idx_agent_checkpoints_created_at ON agent_checkpoints(created_at);

-- 自动清理过期检查点的触发器
CREATE OR REPLACE FUNCTION cleanup_old_checkpoints()
RETURNS void AS $$
BEGIN
    DELETE FROM agent_checkpoints
    WHERE created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- 每日清理任务
SELECT cron.schedule('cleanup-checkpoints', '0 2 * * *', 'SELECT cleanup_old_checkpoints();');
```

## 5. API 与实时通信

### 5.1 RESTful API(FastAPI) 契约

所有 API 端点都以 `/api/v1` 为前缀。以下是完整的 API 契约规范。

#### 5.1.1 统一响应格式

所有API响应都遵循统一的格式规范：

```python
# 成功响应格式
class APIResponse(BaseModel):
    success: bool = True
    data: Any
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None  # 用于请求追踪

# 错误响应格式
class APIError(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None

# 分页响应格式
class PaginatedResponse(BaseModel):
    success: bool = True
    data: List[Any]
    pagination: PaginationInfo
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class PaginationInfo(BaseModel):
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
```

#### 5.1.2 完整API端点规范

| **资源**     | **方法** | **路径**                          | **描述**                             | **请求体**                            | **响应体**                            | **状态码** |
| ------------ | -------- | --------------------------------- | ------------------------------------ | ------------------------------------- | ------------------------------------- | ---------- |
| **认证**     | `POST`   | `/api/v1/auth/register`           | 用户注册                             | `UserRegisterRequest`                 | `TokenResponse`                       | 201        |
|              | `POST`   | `/api/v1/auth/login`              | 用户登录，返回 JWT                   | `UserLoginRequest`                    | `TokenResponse`                       | 200        |
|              | `POST`   | `/api/v1/auth/refresh`            | 刷新访问令牌                         | `RefreshTokenRequest`                 | `TokenResponse`                       | 200        |
|              | `GET`    | `/api/v1/auth/me`                 | 获取当前用户信息                     | -                                     | `UserInfoResponse`                    | 200        |
|              | `PUT`    | `/api/v1/auth/password`           | 修改密码                             | `ChangePasswordRequest`               | `Dict[str, Any]`                      | 200        |
| **订单**     | `GET`    | `/api/v1/orders`                  | 获取订单列表（分页）                 | Query: `symbol`, `status`, `limit`... | `PaginatedResponse[OrderResponse]`    | 200        |
|              | `GET`    | `/api/v1/orders/{order_id}`       | 获取单个订单详情                     | -                                     | `APIResponse[OrderResponse]`          | 200        |
| **配置**     | `GET`    | `/api/v1/configs/exchange`        | 获取所有交易所配置                   | -                                     | `APIResponse[List[ExchangeConfig]]`   | 200        |
|              | `POST`   | `/api/v1/configs/exchange`        | 新增交易所配置                       | `ExchangeConfigCreate`                | `APIResponse[ExchangeConfig]`         | 201        |
|              | `PUT`    | `/api/v1/configs/exchange/{id}`   | 更新交易所配置                       | `ExchangeConfigUpdate`                | `APIResponse[ExchangeConfig]`         | 200        |
|              | `DELETE` | `/api/v1/configs/exchange/{id}`   | 删除交易所配置                       | -                                     | `APIResponse[None]`                   | 204        |
|              | `POST`   | `/api/v1/configs/exchange/{id}/test` | 测试交易所连接                    | -                                     | `APIResponse[ConnectionTestResult]`   | 200        |
|              | `GET`    | `/api/v1/configs/risk`            | 获取用户风控配置                     | -                                     | `APIResponse[RiskConfig]`             | 200        |
|              | `PUT`    | `/api/v1/configs/risk`            | 更新用户风控配置                     | `RiskConfigUpdate`                    | `APIResponse[RiskConfig]`             | 200        |
|              | `POST`   | `/api/v1/configs/risk/reset`      | 重置为默认风控配置                   | -                                     | `APIResponse[RiskConfig]`             | 200        |
|              | `GET`    | `/api/v1/configs/system`          | 获取系统配置信息                     | -                                     | `Dict[str, Any]`                      | 200        |
| **待办动作** | `GET`    | `/api/v1/pending-actions`         | 获取待用户确认的动作列表             | Query: `status`, `limit`...           | `PaginatedResponse[PendingAction]`    | 200        |
|              | `POST`   | `/api/v1/actions/{action_id}/respond` | 用户响应待确认动作               | `ActionResponseRequest`               | `APIResponse[PendingAction]`          | 200        |
| **条件订单** | `GET`    | `/api/v1/conditional-orders`      | 获取条件订单列表                     | Query: `status`, `symbol`...          | `PaginatedResponse[ConditionalOrder]` | 200        |
|              | `POST`   | `/api/v1/conditional-orders`      | 创建条件订单                         | `ConditionalOrderCreate`              | `APIResponse[ConditionalOrder]`       | 201        |
|              | `PUT`    | `/api/v1/conditional-orders/{id}` | 更新条件订单                         | `ConditionalOrderUpdate`              | `APIResponse[ConditionalOrder]`       | 200        |
|              | `DELETE` | `/api/v1/conditional-orders/{id}` | 删除条件订单                         | -                                     | `APIResponse[None]`                   | 204        |
| **信号**     | `GET`    | `/api/v1/signals`                 | 获取信号列表（分页）                 | Query: `platform`, `ai_parse_status`... | `PaginatedResponse[SignalResponse]`   | 200        |
|              | `GET`    | `/api/v1/signals/{signal_id}`     | 获取单个信号详情                     | -                                     | `APIResponse[SignalDetailResponse]`   | 200        |
|              | `POST`   | `/api/v1/signals`                 | 创建新信号（手动）                   | `CreateSignalRequest`                 | `APIResponse[SignalResponse]`         | 201        |
|              | `PUT`    | `/api/v1/signals/{signal_id}`     | 更新信号状态                         | `UpdateSignalRequest`                 | `APIResponse[SignalResponse]`         | 200        |
|              | `DELETE` | `/api/v1/signals/{signal_id}`     | 删除信号                             | -                                     | `APIResponse[None]`                   | 204        |
|              | `GET`    | `/api/v1/signals/stats`           | 获取信号统计信息                     | Query: `date_from`, `date_to`...      | `APIResponse[SignalStatsResponse]`    | 200        |
| **Agent**    | `POST`   | `/api/v1/agent/process`           | 提交新的交易指令处理                 | `ProcessSignalRequest`                | `APIResponse[TaskCreatedResponse]`    | 202        |
|              | `GET`    | `/api/v1/agent/status/{task_id}`  | 获取任务状态或最新检查点             | -                                     | `APIResponse[AgentCheckpoint]`        | 200        |
|              | `GET`    | `/api/v1/agent/tasks`             | 获取用户的任务历史                   | Query: `status`, `limit`...           | `PaginatedResponse[AgentTask]`        | 200        |
| **系统**     | `GET`    | `/api/v1/health`                  | 健康检查                             | -                                     | `APIResponse[HealthStatus]`           | 200        |
|              | `GET`    | `/api/v1/metrics`                 | 系统指标                             | -                                     | `APIResponse[SystemMetrics]`          | 200        |
|              | `GET`    | `/api/v1/version`                 | 系统版本信息                         | -                                     | `APIResponse[VersionInfo]`            | 200        |
|              | `GET`    | `/api/v1/status`                  | 系统综合状态                         | -                                     | `APIResponse[dict]`                   | 200        |

#### 5.1.3 错误状态码规范

| **状态码** | **含义**           | **使用场景**                           |
| ---------- | ------------------ | -------------------------------------- |
| 200        | 成功               | 请求成功处理                           |
| 201        | 创建成功           | 资源创建成功                           |
| 204        | 无内容             | 删除成功或无返回内容                   |
| 400        | 请求错误           | 参数验证失败、业务逻辑错误             |
| 401        | 未认证             | 缺少或无效的认证信息                   |
| 403        | 权限不足           | 用户无权限访问资源                     |
| 404        | 资源不存在         | 请求的资源不存在                       |
| 409        | 冲突               | 资源冲突（如重复创建）                 |
| 422        | 实体无法处理       | 请求格式正确但语义错误                 |
| 429        | 请求过于频繁       | 触发限流                               |
| 500        | 内部服务器错误     | 服务器内部错误                         |
| 502        | 网关错误           | 上游服务错误                           |
| 503        | 服务不可用         | 服务临时不可用                         |

#### 5.1.4 请求/响应模型补充

```python
# 新增的请求模型
class ProcessSignalRequest(BaseModel):
    text: str = Field(..., description="交易指令文本")
    priority: str = Field(default="normal", description="优先级")
    source: str = Field(default="manual", description="信号来源")

class CloseOrderRequest(BaseModel):
    reason: Optional[str] = Field(None, description="关闭原因")
    partial_quantity: Optional[Decimal] = Field(None, description="部分关闭数量")

class BatchActionResponseRequest(BaseModel):
    responses: List[ActionResponse] = Field(..., description="批量响应列表")

class ActionResponse(BaseModel):
    action_id: str = Field(..., description="动作ID")
    decision: str = Field(..., description="决策：approve/reject")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")

# 新增的响应模型
class TaskCreatedResponse(BaseModel):
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")

class HealthStatus(BaseModel):
    status: str = Field(..., description="系统状态")
    database: str = Field(..., description="数据库状态")
    external_services: Dict[str, str] = Field(..., description="外部服务状态")
    uptime: int = Field(..., description="运行时间（秒）")

class SystemMetrics(BaseModel):
    active_connections: int = Field(..., description="活跃连接数")
    active_tasks: int = Field(..., description="活跃任务数")
    memory_usage: float = Field(..., description="内存使用率")
    cpu_usage: float = Field(..., description="CPU使用率")
```

响应格式：

```python
# 成功响应格式
class APIResponse(BaseModel):
    success: bool = True
    data: Any
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# 错误响应格式
class APIError(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# 分页响应格式
class PaginatedResponse(BaseModel):
    success: bool = True
    data: List[Any]
    pagination: Dict[str, Any]  # {"total": 100, "page": 1, "size": 20, "pages": 5}
    timestamp: datetime = Field(default_factory=datetime.utcnow)
```

### 5.2 WebSocket 连接管理架构

#### 5.2.1 增强的WebSocket管理器

我们实现了一个功能完整的WebSocket连接管理器，提供以下核心功能：

**连接管理**：
```python
class WebSocketManager:
    def __init__(self):
        self.connections: Dict[str, WebSocketConnection] = {}
        self.user_connections: Dict[int, Set[str]] = {}
        self.connection_stats = ConnectionStats()

    async def connect(self, websocket: WebSocket, user: User) -> str:
        """建立新的WebSocket连接"""

    async def disconnect(self, connection_id: str):
        """断开WebSocket连接"""

    async def send_to_user(self, user_id: int, message: dict):
        """向特定用户的所有连接发送消息"""

    async def broadcast(self, message: dict, exclude_users: Set[int] = None):
        """广播消息到所有连接"""
```

**连接状态跟踪**：
- 连接唯一标识符生成
- 用户身份验证和授权
- 连接生命周期管理
- 连接统计信息收集

**心跳检测机制**：
```python
class HeartbeatManager:
    def __init__(self, interval: int = 30, timeout: int = 60):
        self.interval = interval  # 心跳间隔（秒）
        self.timeout = timeout    # 超时时间（秒）

    async def start_heartbeat(self, connection_id: str):
        """启动心跳检测"""

    async def handle_pong(self, connection_id: str):
        """处理客户端心跳响应"""

    async def check_timeouts(self):
        """检查超时连接并清理"""
```

#### 5.2.2 事件总线系统

实现了基于发布-订阅模式的事件总线：

```python
class EventBus:
    def __init__(self):
        self.subscribers: Dict[EventType, Set[int]] = {}
        self.user_subscriptions: Dict[int, Set[EventType]] = {}

    def subscribe_user(self, user_id: int, event_types: List[EventType]):
        """用户订阅特定事件类型"""

    def unsubscribe_user(self, user_id: int, event_types: List[EventType]):
        """用户取消订阅特定事件类型"""

    async def publish(self, event_type: EventType, payload: dict, target_users: Set[int] = None):
        """发布事件到订阅用户"""
```

**支持的事件类型**：
```python
class EventType(str, Enum):
    ORDER_UPDATE = "ORDER_UPDATE"                           # 订单状态更新
    AGENT_STATE_TRANSITION = "AGENT_STATE_TRANSITION"       # Agent状态转换
    PENDING_ACTION_REQUIRED = "PENDING_ACTION_REQUIRED"     # 需要用户确认的动作
    NOTIFICATION = "NOTIFICATION"                           # 通知消息
    SYSTEM_STATUS = "SYSTEM_STATUS"                         # 系统状态变更
    MARKET_DATA_UPDATE = "MARKET_DATA_UPDATE"               # 市场数据更新
    RISK_ALERT = "RISK_ALERT"                               # 风险警报
    CONDITIONAL_ORDER_TRIGGER = "CONDITIONAL_ORDER_TRIGGER" # 条件订单触发
    PERFORMANCE_METRICS = "PERFORMANCE_METRICS"             # 性能指标
    TASK_COMPLETED = "TASK_COMPLETED"                       # 任务完成
    HEARTBEAT = "HEARTBEAT"                                 # 心跳检测
```

**事件载荷结构**：
```python
# 基础消息结构
class WebSocketMessage(BaseModel):
    event_type: str
    payload: Any
    timestamp: Optional[str] = None

# 具体事件示例
class OrderUpdateEvent(WebSocketMessage):
    event_type: Literal["ORDER_UPDATE"] = "ORDER_UPDATE"
    payload: Order

class AgentStateTransitionEvent(WebSocketMessage):
    event_type: Literal["AGENT_STATE_TRANSITION"] = "AGENT_STATE_TRANSITION"
    payload: AgentStateTransitionPayload

class PendingActionRequiredEvent(WebSocketMessage):
    event_type: Literal["PENDING_ACTION_REQUIRED"] = "PENDING_ACTION_REQUIRED"
    payload: PendingActionPayload

class NotificationEvent(WebSocketMessage):
    event_type: Literal["NOTIFICATION"] = "NOTIFICATION"
    payload: NotificationPayload
```

#### 5.2.3 消息可靠性保证

**消息确认机制**：
```python
class MessageReliability:
    def __init__(self):
        self.pending_messages: Dict[str, PendingMessage] = {}
        self.ack_timeout = 30  # 确认超时时间

    async def send_with_ack(self, connection_id: str, message: dict) -> bool:
        """发送需要确认的消息"""

    async def handle_ack(self, connection_id: str, message_id: str):
        """处理消息确认"""

    async def resend_unacked_messages(self):
        """重发未确认的消息"""
```

**重连处理**：
- 客户端断线重连支持
- 消息队列缓存
- 状态同步机制

### 5.2.4 WebSocket 通信时序图

```mermaid
sequenceDiagram
    participant Client as 前端客户端
    participant WS as WebSocket管理器
    participant EventBus as 事件总线
    participant Agent as AI Agent
    participant DB as 数据库

    Client->>WS: 建立WebSocket连接
    WS->>WS: 生成连接ID，验证用户
    WS->>Client: 连接确认 + 连接ID

    Client->>WS: 订阅事件类型
    WS->>EventBus: 注册用户订阅
    EventBus->>Client: 订阅确认

    loop 心跳检测
        WS->>Client: PING
        Client->>WS: PONG
        WS->>WS: 更新连接状态
    end

    Client->>WS: 发送交易指令
    WS->>Agent: 启动Agent处理

    loop Agent执行过程
        Agent->>DB: 保存状态检查点
        Agent->>EventBus: 发布状态变更事件
        EventBus->>WS: 推送到订阅用户
        WS->>Client: 发送状态更新（带消息ID）
        Client->>WS: 消息确认（ACK）
    end

    alt 需要用户确认
        Agent->>EventBus: 发布确认请求事件
        EventBus->>WS: 推送确认请求
        WS->>Client: 发送确认请求
        Client->>WS: 发送用户响应
        WS->>Agent: 传递用户响应
        Agent->>Agent: 恢复执行
    end

    Agent->>EventBus: 发布执行结果
    EventBus->>WS: 推送最终结果
    WS->>Client: 发送执行结果
```

**端点**: `wss://your.domain/ws`。客户端在连接时，应通过子协议或首条消息发送 JWT Token 进行认证。

**核心消息类型 (Server -> Client)**：所有消息都遵循一个基础格式：`{ "event_type": "EVENT_NAME", "payload": { ... } }`

| 事件类型                  | 描述                                                        | Payload 格式                                                           |
| ------------------------- | ----------------------------------------------------------- | ---------------------------------------------------------------------- |
| `ORDER_UPDATE`            | 当任何订单的状态（创建、更新、关闭、失败）发生变化时推送。  | 完整的  `Order`  模型对象                                              |
| `SIGNAL_RECEIVED`         | 新信号接收通知                                              | `{ "signal_id": "...", "platform": "discord", "content": "..." }`     |
| `SIGNAL_PARSED`           | AI解析完成通知                                              | `{ "signal_id": "...", "confidence": 0.85, "message_type_ai": "..." }`|
| `SIGNAL_PROCESSED`        | 信号处理状态更新通知                                        | `{ "signal_id": "...", "is_processed": true, "processed_at": "..." }`  |
| `NOTIFICATION`            | 推送系统级或用户级的通知。                                  | `{ "level": "info" \| "warning" \| "error", "message": "通知内容" }`   |
| `AGENT_STATE_TRANSITION`  | 实时推送 Agent 当前正在执行的节点，提供细粒度的"思考"过程。 | `{ "task_id": "...", "from_node": "Parse", "to_node": "Context" }`     |
| `PENDING_ACTION_REQUIRED` | 当有需要用户确认的动作时推送。                              | `{ "action_id": "...", "details": { ... } }`，包含 AI 的提问和建议方案 |
| `SYSTEM_STATUS`           | 系统关键组件（如 Discord 监听器）状态更新。                 | `{ "component": "discord_listener", "status": "online" \| "offline" }` |
| `MARKET_DATA_UPDATE`      | 用户关注的交易对价格实时更新。                              | `{ "symbol": "BTC/USDT", "price": "68000.50" }`                        |

> **注意**: 在不使用 Redis 的初期版本中，WebSocket 连接由单个 FastAPI 进程管理。这意味着如果部署多个后端实例，用户将无法收到其他实例发出的通知。这对于单实例部署的 MVP 来说是可以接受的。未来扩展时，可以引入 Redis 作为消息总线来支持多实例部署。

## 6. Discord 集成设计 (Discord Integration Design)

### 6.1 架构概述

Discord集成是系统的核心信号源，负责实时监听指定频道的交易信号并将其转换为Agent可处理的任务。整个集成采用了**智能信号识别**、**消息去重**、**自动重连**等先进技术，确保信号处理的准确性和系统的高可用性。

#### 核心组件

```mermaid
graph TD
    subgraph "Discord 集成架构"
        A[Discord API] --> B[TradingSignalClient]
        B --> C[MessageDeduplicator]
        B --> D[SignalProcessor]
        B --> E[DiscordListenerManager]

        C --> F[消息去重逻辑]
        D --> G[智能信号识别]
        E --> H[生命周期管理]

        F --> I[Agent Core]
        G --> I
        H --> J[系统监控API]
    end

    style B fill:#e6f2ff,stroke:#333,stroke-width:2px
    style I fill:#ffe6e6,stroke:#333,stroke-width:2px
```

### 6.2 智能信号处理

#### 信号识别算法

系统采用多层次的信号识别算法，能够准确识别各种形式的交易信号：

**识别维度**：
- **交易动作**: buy, sell, long, short, 买入, 卖出, 做多, 做空
- **加密货币符号**: BTC, ETH, ADA, SOL 等 35+ 主流币种
- **交易术语**: target, tp, sl, stop, entry, exit, 目标, 止盈, 止损
- **价格表达式**: $50k, 100 USDT, 10%, x10 等格式
- **交易emoji**: 🚀, 📈, 📉, 💎, 🔥, 💰 等

**信号强度评估**：
```python
def calculate_signal_strength(content: str) -> float:
    """
    计算信号强度（0.0-1.0）
    - 交易动作词: 权重 0.3
    - 加密货币符号: 权重 0.2
    - 交易术语: 权重 0.1
    - 价格信息: 权重 0.2
    - 交易emoji: 权重 0.05
    """
```

#### 消息过滤机制

**排除模式**：
- 问候语: hi, hello, good morning, gm
- 感谢语: thanks, thank you, thx
- 简单回复: yes, no, ok, lol
- 纯数字或符号: 123, !@#$%
- 过短内容: 少于3个字符

**包含模式**：
- 必须包含至少一个交易相关元素
- 支持频道特定的过滤关键词
- 基于信号强度的阈值过滤

### 6.3 消息去重系统

#### 去重算法

```python
class MessageDeduplicator:
    def __init__(self, window_minutes: int = 5, max_cache_size: int = 1000):
        self.window_minutes = window_minutes
        self.max_cache_size = max_cache_size
        self.message_hashes = {}
        self.hash_timestamps = {}

    def is_duplicate(self, content: str, author_id: int, channel_id: int) -> bool:
        """
        基于内容、作者、频道的组合哈希进行去重
        - 内容标准化: 去除多余空格、转换大小写
        - 时间窗口: 只在指定时间窗口内检查重复
        - 自动清理: 定期清理过期的哈希记录
        """
```

#### 性能优化

- **内存管理**: 限制缓存大小，自动清理过期记录
- **哈希算法**: 使用SHA-256确保哈希唯一性
- **时间复杂度**: O(1)查找，O(n)清理（n为过期记录数）

### 6.4 错误处理与重连机制

#### 连接状态管理

```python
class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"
```

#### 智能重连策略

**重连触发条件**：
- 网络连接断开
- Discord API限流
- 认证失效
- 服务器错误

**重连算法**：
```python
async def reconnect_with_backoff():
    """
    指数退避重连算法
    - 初始延迟: 30秒
    - 最大尝试次数: 5次
    - 退避策略: 30s -> 60s -> 120s -> 240s -> 480s
    """
```

#### 错误分类处理

- **可恢复错误**: 网络断开、临时服务不可用 → 自动重连
- **认证错误**: Token无效、权限不足 → 记录错误，停止重连
- **配置错误**: 频道ID无效、配置格式错误 → 记录错误，跳过该频道

### 6.5 监控与可观测性

#### 系统监控API

| 端点 | 方法 | 功能 | 响应数据 |
|------|------|------|----------|
| `/api/v1/system/discord/status` | GET | 获取Discord监听器状态 | 连接状态、监控频道数、重连次数等 |
| `/api/v1/system/discord/stats` | GET | 获取处理统计信息 | 消息处理数、过滤数、成功率等 |
| `/api/v1/system/discord/start` | POST | 手动启动监听器 | 操作结果 |
| `/api/v1/system/discord/stop` | POST | 手动停止监听器 | 操作结果 |
| `/api/v1/system/discord/restart` | POST | 重启监听器 | 操作结果 |

#### 性能指标

- **消息处理速度**: 平均每条消息 < 1ms
- **内存使用**: 消息缓存占用 < 10MB（1000条消息）
- **重连时间**: 网络断开后 < 30秒自动重连
- **信号识别准确率**: > 95%（基于测试用例）

#### 日志记录

```python
# 结构化日志示例
logger.info("Discord message processed",
    channel_id=message.channel.id,
    author=message.author.display_name,
    signal_strength=metadata["signal_strength"],
    processing_time_ms=processing_time * 1000,
    is_duplicate=is_duplicate
)
```

### 6.6 配置与部署

#### 环境变量配置

```bash
# Discord基础配置
DISCORD_TOKEN=your_discord_user_token
DISCORD_AUTO_START=true

# 性能调优
DISCORD_RECONNECT_ATTEMPTS=5
DISCORD_RECONNECT_DELAY=30
DISCORD_MESSAGE_CACHE_SIZE=1000
DISCORD_DEDUPLICATION_WINDOW=5

# Discord过滤和监控配置 - 简化的统一配置格式
DISCORD_FILTER_CONFIG='{
  "enabled": false,
  "server_ids": ["123456789"],
  "channel_ids": ["987654321", "123456789"],
  "author_ids": ["555666777"],
  "allowed_message_types": ["text", "embed"]
}'

# 默认用户ID - 用于存储所有捕获的消息
DEFAULT_USER_ID=708db973-fc1f-4be0-9c52-a9736a10372c
```

#### 生产环境部署

**Docker配置**：
```dockerfile
# Discord集成所需的额外依赖
RUN pip install discord.py-self==2.0.0

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/api/v1/system/discord/status || exit 1
```

**监控告警**：
- Discord连接断开超过5分钟
- 消息处理失败率超过5%
- 内存使用超过阈值
- 重连次数超过限制

## 7 测试策略 (Testing Strategy)

为保证系统质量，我们将采用分层、全面的测试策略。

- **单元测试 (Unit Tests)**: (`pytest`)
  - 针对独立的函数和类进行测试，特别是 LangGraph 的各个节点逻辑函数、工具函数和复杂的业务逻辑。
  - **属性测试 (Property-Based Testing)**：引入 `hypothesis` 库对核心 Pydantic 模型进行测试。Hypothesis 会自动生成大量有效、边界和异常的数据来验证模型的解析、验证和序列化逻辑，能够发现手写单元测试难以覆盖的边缘案例。
- **集成测试 (Integration Tests)**:
  - 测试模块间的交互，如 API 层与数据库的交互、Agent Core 与其调用的工具的交互。
  - **Discord集成测试**: 测试消息去重、信号识别、错误处理等核心功能。
  - 使用数据库事务回滚来隔离测试用例，确保测试环境的纯净。
- **端到端测试 (E2E Tests)**: (`Playwright` 或 `Cypress`)
  - 模拟真实用户操作，验证核心业务流程的完整性。例如，自动化测试“从 Discord 发送一条交易信号，到前端仪表盘最终能看到对应订单被创建”的完整链路。
- **仿真模式 (Simulation Mode)**:
  - 通过**依赖注入**实现。创建一个 `MockExchange` 类，它实现与真实 `ccxt` 交易所完全相同的接口，但在内存中模拟订单撮合、成交和状态变更。
  - 此模式可通过**环境变量**轻松启用，用于在不产生实际资金风险的情况下，对 AI Agent 的决策逻辑进行回归测试和功能验证。

### 7.1 Discord 集成测试策略

#### 单元测试覆盖

```python
# 测试文件结构
tests/
├── unit/
│   └── test_discord_components.py     # Discord组件单元测试
├── integration/
│   └── test_discord_integration.py   # Discord集成测试
├── e2e/
│   └── test_discord_e2e.py          # Discord端到端测试
└── conftest.py                       # Discord测试fixtures
```

**核心测试用例**：

1. **消息去重测试**:
   ```python
   def test_message_deduplication():
       deduplicator = MessageDeduplicator(window_minutes=1)
       assert not deduplicator.is_duplicate("buy btc", 123, 456)  # 首次
       assert deduplicator.is_duplicate("buy btc", 123, 456)      # 重复
   ```

2. **信号识别测试**:
   ```python
   @pytest.mark.parametrize("content,expected", [
       ("buy btc", True),
       ("sell eth at $3000", True),
       ("hello world", False),
       ("good morning", False),
   ])
   def test_signal_recognition(content, expected):
       processor = SignalProcessor()
       assert processor.should_process_message(content) == expected
   ```

3. **性能测试**:
   ```python
   def test_processing_performance():
       processor = SignalProcessor()
       messages = ["buy btc", "sell eth"] * 1000

       start_time = time.time()
       for message in messages:
           processor.should_process_message(message)
       processing_time = time.time() - start_time

       assert processing_time < 1.0  # 应在1秒内完成
   ```

#### 集成测试策略

**模拟Discord环境**:
```python
@pytest.fixture
def mock_discord_message():
    def _create_message(content="buy btc", channel_id=123, author_id=456):
        message = Mock()
        message.content = content
        message.channel.id = channel_id
        message.author.id = author_id
        return message
    return _create_message

async def test_message_processing_flow(mock_discord_message):
    client = TradingSignalClient()
    message = mock_discord_message("buy btc at $50k")

    with patch('aiohttp.ClientSession.post') as mock_post:
        await client.on_message(message)
        mock_post.assert_called_once()  # 验证API调用
```

#### 端到端测试

**完整流程验证**:
```python
@pytest.mark.e2e
async def test_discord_to_agent_flow():
    """测试从Discord消息到Agent处理的完整流程"""
    # 1. 模拟Discord消息
    # 2. 验证信号识别
    # 3. 验证Agent任务创建
    # 4. 验证WebSocket通知
    # 5. 验证前端状态更新
```

#### 测试工具和脚本

**快速验证脚本**:
```bash
# 运行Discord集成测试
python scripts/test_discord.py

# 运行特定测试套件
pytest tests/integration/test_discord_integration.py -v
pytest tests/unit/test_discord_components.py -v
pytest tests/e2e/test_discord_e2e.py -v
```

## 7. 配置管理系统设计

### 7.1 分层配置架构

我们实现了一个完整的分层配置系统，支持多种配置源和自动类型验证：

#### 配置层次结构

```python
class Settings(BaseSettings):
    """主配置类 - 统一管理所有配置"""

    # 应用基础配置
    app_name: str = "AI Crypto Trading Agent"
    app_version: str = "0.1.0"
    debug: bool = False
    environment: str = "development"

    # 数据库配置
    database: DatabaseSettings = DatabaseSettings()

    # 安全配置
    security: SecuritySettings = SecuritySettings()

    # API配置
    api: APISettings = APISettings()

    # LLM配置
    llm: LLMSettings = LLMSettings()

    # Discord配置
    discord: DiscordSettings = DiscordSettings()

    # 交易配置
    trading: TradingSettings = TradingSettings()

    # 日志配置
    logging: LoggingSettings = LoggingSettings()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        case_sensitive=False
    )
```

#### 配置源优先级

1. **环境变量** (最高优先级)
2. **.env 文件**
3. **默认值** (最低优先级)

#### 嵌套配置支持

```bash
# 环境变量示例
DATABASE__URL=postgresql://user:pass@localhost/db
DATABASE__POOL_SIZE=10
LLM__OPENAI_API_KEY=sk-xxx
LLM__DEFAULT_MODEL=gpt-4
SECURITY__JWT_SECRET_KEY=your-secret-key
```

### 7.2 配置验证和类型转换

#### 自定义验证器

```python
class DatabaseSettings(BaseModel):
    url: str = "postgresql+asyncpg://postgres:postgres@localhost/crypto_trader"
    pool_size: int = Field(default=5, ge=1, le=50)

    @validator('url')
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'postgresql+asyncpg://')):
            raise ValueError('Database URL must use PostgreSQL')
        return v

class LLMSettings(BaseModel):
    openai_api_key: Optional[str] = None
    default_model: str = "gpt-4"

    @validator('openai_api_key')
    def validate_api_key(cls, v):
        if v and not v.startswith('sk-'):
            raise ValueError('OpenAI API key must start with sk-')
        return v
```

#### 动态配置解析

```python
class APISettings(BaseModel):
    cors_origins_raw: Optional[str] = None

    @property
    def cors_origins(self) -> List[str]:
        """动态解析CORS origins配置"""
        if not self.cors_origins_raw:
            return ["http://localhost:3000", "http://localhost:8080"]

        # 支持JSON数组格式
        if self.cors_origins_raw.startswith("["):
            return json.loads(self.cors_origins_raw)

        # 支持逗号分隔格式
        return [origin.strip() for origin in self.cors_origins_raw.split(",")]
```

#### Discord 配置详细说明

Discord集成是系统的核心组件之一，负责实时监听交易信号。配置系统采用简化的统一过滤配置：

```python
class DiscordFilterConfig(BaseModel):
    """Discord消息过滤配置 - 简化版本"""

    # 全局过滤开关
    enabled: bool = Field(default=False, description="是否启用消息过滤")

    # 核心过滤条件 - 使用Set提高查找性能（O(1)时间复杂度）
    server_ids: Set[str] = Field(default_factory=set, description="允许的服务器ID集合")
    channel_ids: Set[str] = Field(default_factory=set, description="允许的频道ID集合")
    author_ids: Set[str] = Field(default_factory=set, description="允许的作者ID集合")

    # 消息类型过滤
    allowed_message_types: Set[DiscordMessageType] = Field(
        default_factory=lambda: {DiscordMessageType.ALL},
        description="允许的消息类型集合"
    )

class DiscordSettings(BaseModel):
    """Discord集成配置 - 简化版本"""
    token: Optional[str] = Field(default=None, env="DISCORD_TOKEN")
    auto_start: bool = Field(default=True, env="DISCORD_AUTO_START")
    reconnect_attempts: int = Field(default=5, env="DISCORD_RECONNECT_ATTEMPTS")
    reconnect_delay: int = Field(default=30, env="DISCORD_RECONNECT_DELAY")
    message_cache_size: int = Field(default=1000, env="DISCORD_MESSAGE_CACHE_SIZE")
    deduplication_window: int = Field(default=5, env="DISCORD_DEDUPLICATION_WINDOW")
```

**环境变量配置示例**：

```bash
# Discord基础配置
DISCORD_TOKEN=your_discord_user_token
DISCORD_AUTO_START=true
DISCORD_RECONNECT_ATTEMPTS=5
DISCORD_RECONNECT_DELAY=30
DISCORD_MESSAGE_CACHE_SIZE=1000
DISCORD_DEDUPLICATION_WINDOW=5

# Discord过滤和监控配置 - 简化的统一配置格式
DISCORD_FILTER_CONFIG='{
  "enabled": false,
  "server_ids": ["123456789"],
  "channel_ids": ["987654321", "123456789"],
  "author_ids": ["555666777"],
  "allowed_message_types": ["text", "embed"]
}'

# 默认用户ID - 用于存储所有捕获的消息
DEFAULT_USER_ID=708db973-fc1f-4be0-9c52-a9736a10372c
```

**配置说明**：
- `token`: Discord用户令牌（使用discord.py-self库）
- `discord_filter_config`: JSON格式的统一过滤配置，支持服务器、频道、作者和消息类型过滤
- `default_user_id`: 默认用户ID，用于存储所有捕获的Discord消息
- `auto_start`: 应用启动时是否自动启动Discord监听器
- `reconnect_attempts`: 连接断开时的最大重连尝试次数
- `reconnect_delay`: 重连延迟时间（秒）
- `message_cache_size`: 消息缓存大小，用于去重和性能优化
- `deduplication_window`: 消息去重时间窗口（分钟）

### 7.3 配置缓存和热重载

#### 配置缓存机制

```python
@lru_cache()
def get_settings() -> Settings:
    """获取缓存的配置实例"""
    return Settings()

# 使用示例
settings = get_settings()
```

#### 配置热重载 (开发环境)

```python
class ConfigManager:
    def __init__(self):
        self._settings = None
        self._last_modified = None

    def get_settings(self, force_reload: bool = False) -> Settings:
        """获取配置，支持热重载"""
        env_file = Path(".env")

        if force_reload or self._should_reload(env_file):
            self._settings = Settings()
            self._last_modified = env_file.stat().st_mtime if env_file.exists() else None

        return self._settings
```

### 7.4 环境特定配置

#### 多环境配置文件

```bash
# 项目根目录
├── .env                    # 默认配置
├── .env.development        # 开发环境
├── .env.testing           # 测试环境
├── .env.production        # 生产环境
└── .env.local             # 本地覆盖配置（不提交到版本控制）
```

#### 环境配置加载逻辑

```python
def load_env_files():
    """按优先级加载环境配置文件"""
    env_files = [
        ".env",
        f".env.{os.getenv('ENVIRONMENT', 'development')}",
        ".env.local"
    ]

    for env_file in env_files:
        if Path(env_file).exists():
            load_dotenv(env_file, override=True)
```

## 8. 技术栈选型

|                    |                        |                                                                                               |
| ------------------ | ---------------------- | --------------------------------------------------------------------------------------------- |
| **领域**           | **技术/框架**          | **备注**                                                                                      |
| **语言**           | Python 3.11+           | 利用最新的类型提示和异步特性。                                                                |
| **Web 框架**       | FastAPI                | 性能卓越，与 Pydantic 无缝集成，原生支持异步和 WebSocket。                                    |
| **AI Agent 框架**  | LangGraph              | 提供确定性、可追溯的状态机。                                                                  |
| **结构化输出**     | Instructor             | 强制 LLM 输出 Pydantic 模型，保证健壮性。                                                     |
| **数据库**         | PostgreSQL             | 功能强大，支持 JSONB 和高级索引，满足当前和未来需求。                                         |
| **ORM**            | SQLAlchemy 2.x (Async) | 成熟可靠的异步 ORM。                                                                          |
| **数据库迁移**     | Alembic                | 管理数据库 Schema 演进。                                                                      |
| **数据验证**       | Pydantic v2 + Settings | **增强的数据验证**：使用 Decimal 确保金融精度，完善的字段验证器，环境变量自动读取和类型转换。 |
| **配置管理**       | Pydantic Settings      | **分层配置系统**：支持环境变量、.env 文件、默认值，自动类型验证和转换。                       |
| **交易所交互**     | ccxt                   | 业界标准的加密货币交易所交互库。                                                              |
| **Discord 集成**   | discord.py-self        | **智能信号监听**：支持用户账号登录，消息去重，智能信号识别，自动重连机制。                    |
| **加密存储**       | cryptography           | 用于 API 密钥的安全加密存储。                                                                 |
| **认证授权**       | python-jose, passlib   | JWT 令牌处理和密码哈希。                                                                      |
| **WebSocket 管理** | 自定义管理器           | **增强的 WebSocket 架构**：用户分组、管理员权限、连接统计、自动重连机制。                     |
| **结构化日志**     | structlog              | **生产级日志**：JSON 格式输出，上下文信息追踪，便于日志聚合和分析。                           |
| **错误处理**       | 自定义装饰器系统       | **统一错误处理**：超时控制、重试机制、异常分类、状态保护。                                    |
| **配置管理**       | 分层配置系统           | **多环境支持**：环境变量、配置文件、默认值，热重载、类型验证。                                |

## 8. 发展路线图

- **第一阶段：核心功能 MVP** ✅ **已完成Discord集成优化**
  - **目标**：系统能够自动执行一条清晰的、来自 Discord 的交易指令。
  - **任务**：搭建基础架构，实现用户认证、交易所配置，构建处理标准指令的 Agent 图，完成前端订单展示。
  - **Discord集成完成项**：
    - ✅ 智能信号识别和消息过滤
    - ✅ 消息去重和缓存机制
    - ✅ 自动重连和错误处理
    - ✅ 多频道监控和配置管理
    - ✅ 系统监控API和可观测性
    - ✅ 完整的测试覆盖
- **第二阶段：增强 AI 与交互**
  - **目标**：系统具备强大的自然语言理解能力和鲁棒性。
  - **任务**：优化 Agent 图，增加错误处理和**用户确认**循环，增强 Prompt 以处理模糊指令，实现条件订单功能。
- **第三阶段：长期学习与扩展**
  - **目标**：系统具备自适应学习和演化能力。
  - **任务**：引入**向量数据库 (Vector DB)**，实现“任务复盘”和 RAG，使 Agent 能从历史经验中学习。引入可观测性套件（OTel, Prometheus）。

## 9. 项目后端目录结构

这是一个“Monorepo”风格的结构，将前端、后端和部署配置清晰地分离在同一个代码仓库中，便于统一管理。

```
/ai-crypto-trading-agent/
|
├── 📄 .env.example                # 环境变量模板文件，必须包含所有必需的配置项
├── 📄 .gitignore                  # Git 忽略文件配置
├── 📄 docker-compose.yml          # Docker Compose 编排文件，用于一键启动整个应用栈
├── 📄 README.md                    # 项目总说明文档，包含架构、部署指南和开发规范
|
├── 📁 backend/                    # 【后端】FastAPI 单体应用
│   ├── 📁 alembic/                  # Alembic 数据库迁移脚本目录
│   │   ├── 📁 versions/              # 自动生成的数据库版本脚本
│   │   └── 📄 script.py.mako      # 迁移脚本模板
│   ├── 📁 app/                     # FastAPI 应用核心源代码
│   │   ├── 📁 api/                   # API 路由层 (Web 层的 "Controller")
│   │   │   └── 📁 v1/                # API 版本 v1
│   │   │       ├── 📄 __init__.py
│   │   │       ├── 📄 actions.py    # 处理 /actions/{action_id}/respond 等路由
│   │   │       ├── 📄 auth.py      # 用户认证、登录路由
│   │   │       ├── 📄 configs.py   # 交易所、风控配置路由
│   │   │       └── 📄 orders.py    # 订单查询路由
│   │   ├── 📁 agent/                  # AI 核心 (LangGraph) 的大脑，系统的决策中枢
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 graph.py         # 定义和装配 LangGraph 状态图
│   │   │   ├── 📄 nodes.py         # 实现状态图中的所有核心节点逻辑 (Parse, Context, Plan...)
│   │   │   ├── 📄 prompts.py        # 集中管理所有 Prompt 模板 (如附录 D 所示)
│   │   │   └── 📄 tools.py         # 定义 Agent 可调用的所有工具函数
│   │   ├── 📁 core/                   # 核心业务逻辑、模型和配置
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 config.py        # 使用 Pydantic 读取环境变量，管理应用配置
│   │   │   ├── 📄 database.py      # SQLAlchemy 引擎、会话管理
│   │   │   ├── 📄 models.py        # SQLAlchemy 数据表模型 (ERD 的代码实现)
│   │   │   ├── 📄 schemas.py       # 【单一事实来源】所有核心 Pydantic 模型 (附录 A)
│   │   │   ├── 📄 security.py      # 加密 (Fernet)、密码哈希、JWT 相关逻辑
│   │   │   ├── 📄 ws_manager.py    # WebSocket 连接管理器 (非 Redis 模式下)
│   │   │   └── 📄 ws_schemas.py    # 【单一事实来源】WebSocket 消息契约 (附录 B)
│   │   ├── 📁 services/               # 后台服务和外部系统交互
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 discord_listener.py # Discord 信号监听服务
│   │   │   ├── 📄 exchange.py        # CCXT 交易所接口的封装
│   │   │   └── 📄 price_watcher.py   # 条件订单的价格观察者
│   │   ├── 📄 __init__.py
│   │   └── 📄 main.py                # FastAPI 应用入口，装载路由和中间件
│   ├── 📁 tests/                   # 测试代码
│   │   ├── 📁 data/                  # 存放测试用的数据 fixture
│   │   ├── 📁 integration/           # 集成测试 (测试服务间的交互)
│   │   └── 📁 unit/                  # 单元测试 (测试独立的函数和类)
│   │   └── 📄 conftest.py            # Pytest 共享 fixtures
│   ├── 📄 alembic.ini                # Alembic 配置文件
│   ├── 📄 Dockerfile                 # 后端服务的 Dockerfile
│   └── 📄 requirements.txt           # **推荐** Anaconda/Miniconda 环境配置文件
|
└── 📁 frontend/                   # 【前端】Vue.js 3 应用
```

**架构设计释义**：

1. **顶级关注点分离 (Top-Level Separation of Concerns)**：
   - `backend/`、`frontend/` 和根目录的 `docker-compose.yml` 形成了清晰的职责边界。后端开发人员、前端开发人员和 DevOps 可以在各自的领域内独立工作。
   - `README.md` 和 `.env.example` 在根目录，为新加入的开发者提供了唯一的、必须阅读的入口点。
2. **后端模块化 (`backend/app/`)**：这是“单一应用，模块化设计”原则的体现。
   - **`api/`**: 严格遵循设计文档中的 API 契约，将所有 HTTP 接口按资源进行组织，使得 Web 层非常薄，只负责请求的接收、校验和转发。
   - **`agent/`**: 这是系统的“大脑”，被明确地独立出来。将 `graph.py` (结构)、`nodes.py` (逻辑)、`tools.py` (能力) 和 `prompts.py` (知识) 分离，使得 AI 的行为和迭代变得极为清晰和可控。
   - **`core/`**: 存放了整个应用的基石。
     - 将 `schemas.py` 和 `ws_schemas.py` 作为“单一事实来源 (Single Source of Truth)”是“契约优先”原则的关键实践，确保了数据在系统各层流转时的一致性和类型安全。
     - `models.py` 独立出来，专门负责与数据库表的映射。
   - **`services/`**: 将所有主动与外部系统（如 Discord、交易所）交互的、可能长时间运行的逻辑封装于此，与被动响应 API 请求的逻辑分离开。
     - **`discord_listener.py`**: 实现了完整的Discord集成功能，包括智能信号识别、消息去重、自动重连等核心能力。
3. **工程化与可维护性**：
   - **`alembic/`**: 将数据库迁移脚本纳入版本控制，并与 `models.py` 并存，是保障部署可靠性、实现“可追溯性”的最佳实践。
   - **`tests/`**: 独立的测试目录和按类型（单元/集成）划分的结构，保障了代码质量和“内建的可测试性”。
   - **`Dockerfile`**: 每个服务都有自己的 `Dockerfile`，实现了环境的封装和部署的标准化。

## 10. 附录

### 🔄 前后端类型同步

#### Pydantic模型规范

为确保前后端数据传输的一致性和精确性，后端采用以下类型规范：

- **金融精度处理**: 所有金融相关字段（价格、数量、盈亏等）统一使用 `Decimal` 类型，确保计算精度
- **序列化标准**: `Decimal` 字段在API响应中自动序列化为字符串格式，前端接收后使用专门的金融计算库处理
- **枚举一致性**: 订单状态、交易方向等枚举值与前端TypeScript定义保持严格一致（如使用 `'failed'` 而非 `'rejected'`）
- **UUID标准**: 所有ID字段使用标准UUID格式，确保全局唯一性和类型安全

#### API响应格式标准

- **统一响应结构**: 所有API响应遵循标准格式，包含 `success`、`data`、`message`、`timestamp` 等字段
- **类型映射规则**: Python `Decimal` → TypeScript `string`，Python `datetime` → TypeScript `string`（ISO格式）
- **错误格式标准**: 统一的错误响应格式，支持前端的分层错误处理机制
- **验证机制**: 使用Pydantic的字段验证器确保数据格式的正确性，为前端运行时验证提供可靠基础

**架构价值**: 通过严格的类型规范和序列化标准，确保前后端数据交互的100%一致性，为金融级应用提供可靠的数据传输保障。

### 附录 A: 核心后端 Pydantic 模型

此附录是所有核心数据结构的**单一事实来源 (Single Source of Truth)**。

```python
# file: core/schemas.py
from typing import List, Dict, Optional, Any, Literal
from pydantic import BaseModel, Field, field_validator
from enum import Enum
import uuid
from datetime import datetime

# --- 数据库表对应模型 ---

class RiskConfig(BaseModel):
    id: Optional[int] = None
    user_id: int
    max_position_size_usd: float = Field(default=1000.0, ge=0)
    max_daily_loss_usd: float = Field(default=500.0, ge=0)
    max_open_orders: int = Field(default=5, ge=1)
    allowed_symbols: List[str] = Field(default_factory=lambda: ["BTC/USDT", "ETH/USDT"])
    confidence_threshold: float = Field(default=0.80, ge=0, le=1, description="置信度阈值，低于此值强制用户确认")
    auto_approve_threshold: float = Field(default=0.95, ge=0, le=1, description="自动批准阈值，高于此值自动执行")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @field_validator('auto_approve_threshold')
    @classmethod
    def validate_threshold_order(cls, v, info):
        """确保auto_approve_threshold >= confidence_threshold"""
        if 'confidence_threshold' in info.data and v < info.data['confidence_threshold']:
            raise ValueError('auto_approve_threshold必须大于等于confidence_threshold')
        return v

class ExchangeConfig(BaseModel):
    id: Optional[int] = None
    user_id: int
    exchange_name: str = Field(..., pattern=r"^(binance|okx|bybit)$")
    api_key: str = Field(..., min_length=1)
    api_secret: str = Field(..., min_length=1)
    passphrase: Optional[str] = None  # For OKX
    sandbox_mode: bool = Field(default=True)
    is_active: bool = Field(default=True)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# --- 基础与工具函数模型 ---

class OrderStatus(str, Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    FAILED = "failed"

class TradeSide(str, Enum):
    BUY = "buy"
    SELL = "sell"

class Order(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: int
    client_order_id: str = Field(..., description="客户端订单ID")
    exchange_order_id: Optional[str] = Field(None, description="交易所订单ID")
    source_message_id: Optional[str] = Field(None, description="来源消息ID")
    symbol: str
    side: TradeSide
    quantity: Decimal = Field(..., description="订单数量，使用Decimal确保精度", gt=0)
    entry_price: Optional[Decimal] = Field(None, description="入场价格", gt=0)
    close_price: Optional[Decimal] = Field(None, description="平仓价格", gt=0)
    pnl: Optional[Decimal] = Field(None, description="盈亏")
    status: OrderStatus
    agent_log: Optional[Dict[str, Any]] = Field(None, description="Agent执行日志")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    closed_at: Optional[datetime] = Field(None, description="平仓时间")

class TradeResult(BaseModel):
    status: Literal["success", "failure"]
    order_id: Optional[str] = None
    client_order_id: str
    error_message: Optional[str] = None

# --- AI 核心 (LangGraph State & Intents) ---

class IntentType(str, Enum):
    CREATE_ORDER = "CREATE_ORDER"
    CLOSE_ORDER = "CLOSE_ORDER"
    MODIFY_ORDER = "MODIFY_ORDER"
    QUERY_STATUS = "QUERY_STATUS"
    AMBIGUOUS = "AMBIGUOUS"

class ParsedIntent(BaseModel):
    """
    Represents a single, structured trading intent parsed from natural language.
    This is a critical data model for the AI core.
    """
    intent_type: IntentType  # 使用枚举类型替代字面量
    raw_text: str = Field(..., description="The original text snippet from the input that led to this intent. Used for logging and debugging.")

    # [OPTIMIZATION] The 'side' is now determined by the LLM in the parsing stage.
    side: Optional[TradeSide] = Field(None, description="The determined trade direction (buy/long or sell/short). Null if cannot be determined.")

    symbol: Optional[str] = Field(None, description="e.g., 'BTC/USDT'")
    quantity_usd: Optional[Decimal] = Field(None, description="The amount in USD. 使用Decimal类型确保金融计算精度.", ge=0)
    target_criteria: Optional[str] = Field(None, description="Criteria to select an order, e.g., 'the profitable one', 'the latest ETH order'.")
    confidence: Decimal = Field(Decimal("1.0"), description="Confidence score of the parsing, from 0.0 to 1.0.", ge=0, le=1)
    clarification_needed: Optional[str] = Field(None, description="If intent is AMBIGUOUS or side is null, explain why and ask a clear question.")

    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        """自动补充交易对后缀并标准化格式"""
        if v and '/' not in v:
            return f"{v.upper()}/USDT"
        return v.upper() if v else v

class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class TradePlan(BaseModel):
    plan_id: uuid.UUID = Field(default_factory=uuid.uuid4)
    symbol: str
    side: TradeSide  # 使用枚举类型
    order_type: OrderType = OrderType.MARKET
    quantity: Decimal = Field(..., description="交易数量，使用Decimal确保精度", gt=0)
    price: Optional[Decimal] = Field(None, description="限价单价格，使用Decimal确保精度", gt=0)

    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        """标准化交易对格式"""
        if '/' not in v:
            return f"{v.upper()}/USDT"
        return v.upper()

    @field_validator('quantity')
    @classmethod
    def validate_quantity(cls, v):
        """验证交易数量"""
        if v <= 0:
            raise ValueError('数量必须大于0')
        return v

class ErrorAnalysis(BaseModel):
    is_correctable: bool = Field(..., description="True if the error is temporary and can be retried, False otherwise.")
    reason: str = Field(..., description="A brief, human-readable explanation of the error.")
    suggestion: str = Field(..., description="A suggestion for the system or user.")

class AgentState(BaseModel):
    task_id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: int
    raw_input: str
    parsed_intents: List[ParsedIntent] = []
    context: Dict[str, Any] = {} # e.g., {'risk_config': RiskConfig, 'active_orders': List[Order], ...}
    execution_plan: List[TradePlan] = []
    error_message: Optional[str] = None
    retry_count: int = 0
    pending_action_id: Optional[uuid.UUID] = None
    user_response: Optional[Dict[str, Any]] = None # To inject user's response
    final_result: Optional[Any] = None
    log: List[str] = []

# --- API 请求/响应模型 ---

class OrderResponse(BaseModel):
    id: int
    user_id: int
    symbol: str
    side: str
    quantity: float
    price: Optional[float]
    order_type: str
    status: str
    exchange_order_id: Optional[str]
    client_order_id: Optional[str]
    filled_quantity: Optional[float]
    average_price: Optional[float]
    fee: Optional[float]
    pnl: Optional[float]
    created_at: datetime
    updated_at: Optional[datetime]

class OrderListResponse(BaseModel):
    orders: List[OrderResponse]
    total: int
    page: int
    size: int

class ExchangeConfigCreate(BaseModel):
    exchange_name: str = Field(..., pattern=r"^(binance|okx|bybit)$")
    api_key: str = Field(..., min_length=1)
    api_secret: str = Field(..., min_length=1)
    passphrase: Optional[str] = None
    sandbox_mode: bool = True

class ExchangeConfigResponse(BaseModel):
    id: int
    user_id: int
    exchange_name: str
    api_key_masked: str
    sandbox_mode: bool
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]

class RiskConfigUpdate(BaseModel):
    max_position_size_usd: Optional[float] = Field(None, ge=0)
    max_daily_loss_usd: Optional[float] = Field(None, ge=0)
    max_open_orders: Optional[int] = Field(None, ge=1)
    allowed_symbols: Optional[List[str]] = None
    confidence_threshold: Optional[float] = Field(None, ge=0, le=1, description="置信度阈值")
    auto_approve_threshold: Optional[float] = Field(None, ge=0, le=1, description="自动批准阈值")

    @field_validator('auto_approve_threshold')
    @classmethod
    def validate_threshold_order(cls, v, info):
        """确保auto_approve_threshold >= confidence_threshold"""
        if v is not None and 'confidence_threshold' in info.data and info.data['confidence_threshold'] is not None:
            if v < info.data['confidence_threshold']:
                raise ValueError('auto_approve_threshold必须大于等于confidence_threshold')
        return v

class RiskConfigResponse(BaseModel):
    id: int
    user_id: int
    max_position_size_usd: float
    max_daily_loss_usd: float
    max_open_orders: int
    allowed_symbols: List[str]
    confidence_threshold: float
    auto_approve_threshold: float
    created_at: datetime
    updated_at: Optional[datetime]

# --- 认证相关模型 ---

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6)

class UserResponse(BaseModel):
    id: int
    username: str
    created_at: datetime

# --- 待处理动作相关模型 ---

class PendingAction(BaseModel):
    id: Optional[int] = None
    user_id: int
    action_type: str
    details: Dict[str, Any]
    expires_at: datetime
    created_at: Optional[datetime] = None

class ConditionalOrderCreate(BaseModel):
    symbol: str
    side: str
    quantity: float
    trigger_price: float
    order_type: str = "market"
    target_price: Optional[float] = None

class ConditionalOrderUpdate(BaseModel):
    trigger_price: Optional[float] = None
    target_price: Optional[float] = None
    is_active: Optional[bool] = None

class ConditionalOrderResponse(BaseModel):
    id: int
    user_id: int
    symbol: str
    side: str
    quantity: float
    trigger_price: float
    order_type: str
    target_price: Optional[float]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]

class PendingActionUpdate(BaseModel):
    user_response: Dict[str, Any]

class PendingActionResponse(BaseModel):
    id: int
    user_id: int
    action_type: str
    details: Dict[str, Any]
    expires_at: datetime
    created_at: datetime
```

### 附录 B: WebSocket 消息契约

```python
# file: core/ws_schemas.py
from pydantic import BaseModel
from typing import Literal, Optional, Any, Dict, List, Union
import uuid
from datetime import datetime
from decimal import Decimal
from .schemas import Order, TradePlan

# ============================================================================
# 基础消息模型
# ============================================================================

class WebSocketMessage(BaseModel):
    """WebSocket消息基类"""
    event_type: str
    payload: Any
    timestamp: Optional[str] = None

    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.utcnow().isoformat()
        super().__init__(**data)

# ============================================================================
# 增强的载荷模型
# ============================================================================

class AgentStateTransitionPayload(BaseModel):
    """Agent状态转换载荷"""
    timestamp: str
    from_node: str
    to_node: str
    task_id: uuid.UUID
    user_id: int
    execution_context: Optional[Dict[str, Any]] = None  # 执行上下文
    error_info: Optional[str] = None  # 错误信息

class PendingActionDetails(BaseModel):
    """待办事项的详细信息，用于发往前端"""
    raw_input: str
    clarification_needed: str
    proposed_plan: Optional[List[TradePlan]] = None
    confidence_score: Optional[float] = None  # 置信度分数
    alternative_interpretations: Optional[List[str]] = None  # 备选解释
    context: Optional[Dict[str, Any]] = None

class PendingActionPayload(BaseModel):
    """待处理动作载荷"""
    action_id: uuid.UUID
    action_type: str  # 动作类型
    details: PendingActionDetails
    expires_at: str
    user_id: int
    priority: str = "normal"  # 优先级：low, normal, high, urgent

class NotificationPayload(BaseModel):
    """通知载荷"""
    title: str
    message: str
    level: Literal["info", "warning", "error", "success"] = "info"
    user_id: Optional[int] = None
    persistent: bool = False  # 是否持久化显示
    action_url: Optional[str] = None  # 可选的操作链接

class SystemStatusPayload(BaseModel):
    """系统状态载荷"""
    component: str  # 组件名称
    status: Literal["online", "offline", "degraded", "maintenance"]
    message: Optional[str] = None
    timestamp: str
    metrics: Optional[Dict[str, Any]] = None  # 性能指标

class MarketDataUpdatePayload(BaseModel):
    """市场数据更新载荷"""
    symbol: str
    price: Decimal
    change_24h: Optional[Decimal] = None
    volume_24h: Optional[Decimal] = None
    timestamp: str

# ============================================================================
# 具体事件模型
# ============================================================================

class OrderUpdateEvent(WebSocketMessage):
    event_type: Literal["ORDER_UPDATE"] = "ORDER_UPDATE"
    payload: Order

class AgentStateTransitionEvent(WebSocketMessage):
    event_type: Literal["AGENT_STATE_TRANSITION"] = "AGENT_STATE_TRANSITION"
    payload: AgentStateTransitionPayload

class PendingActionRequiredEvent(WebSocketMessage):
    event_type: Literal["PENDING_ACTION_REQUIRED"] = "PENDING_ACTION_REQUIRED"
    payload: PendingActionPayload

class NotificationEvent(WebSocketMessage):
    event_type: Literal["NOTIFICATION"] = "NOTIFICATION"
    payload: NotificationPayload

class SystemStatusEvent(WebSocketMessage):
    event_type: Literal["SYSTEM_STATUS"] = "SYSTEM_STATUS"
    payload: SystemStatusPayload

class MarketDataUpdateEvent(WebSocketMessage):
    event_type: Literal["MARKET_DATA_UPDATE"] = "MARKET_DATA_UPDATE"
    payload: MarketDataUpdatePayload

# ============================================================================
# 联合类型定义
# ============================================================================

WebSocketEventUnion = Union[
    OrderUpdateEvent,
    AgentStateTransitionEvent,
    PendingActionRequiredEvent,
    NotificationEvent,
    SystemStatusEvent,
    MarketDataUpdateEvent
]

# ============================================================================
# WebSocket管理器增强功能
# ============================================================================

class ConnectionStats(BaseModel):
    """连接统计信息"""
    total_connections: int = 0
    authenticated_connections: int = 0
    anonymous_connections: int = 0
    admin_connections: int = 0
    user_distribution: Dict[int, int] = {}  # user_id -> connection_count

class UserConnectionInfo(BaseModel):
    """用户连接信息"""
    user_id: int
    connection_count: int
    is_admin: bool = False
    last_activity: str
    connected_since: str
```

### 附录 C: 基线 Prompt 示例

#### 节点 B (解析指令) Prompt

````
You are an expert trading assistant responsible for parsing natural language trading signals in both **English and Chinese** into a structured JSON format.
Your task is to analyze the user's input by following a strict step-by-step process, and then format your final conclusion into a list of structured intent objects.

**Your thought process MUST follow these steps:**
1.  **Analyze Keywords**: Identify all keywords related to assets (e.g., 'BTC', 'ETH'), actions (e.g., 'long', 'short', 'tp', 'sl', '平仓', '做多'), quantities (e.g., '100U', '25%'), and conditions.
2.  **Deconstruct into Intents**: Break down the user's single message into one or more atomic intents. A single message like "short eth at 1800, set sl to 1850" contains two intents: one to create an order and one to modify it.
3.  **Determine Intent Type and Side for Each Intent**:
    - For `CREATE_ORDER`, determine the `side` ('buy' or 'sell'). If no direction is given but a price is mentioned (e.g., "btc 68000"), default to `side: "buy"`.
    - For `CLOSE_ORDER` (e.g., `tp`, `sl`, `平仓`), the `side` must be the *opposite* of the position being closed. Assume `tp` on a long position, so the closing `side` is 'sell'.
    - For `PARTIAL_CLOSE_ORDER` (e.g., `tp1`, `减仓`), infer a percentage and the opposite `side`.
    - For `MODIFY_ORDER` or `QUERY_STATUS`, `side` should be `null`.
4.  **Extract Parameters**: For each intent, extract all parameters like `symbol`, `quantity_usd`, `quantity_percentage`, etc. Standardize the symbol (e.g., 'btc' -> 'BTC/USDT').
5.  **Assess Confidence and Ambiguity**: For each intent, assign a confidence score. If any crucial information (like `side` for a `CREATE_ORDER` intent) is missing and cannot be inferred, you MUST set the `intent_type` to `AMBIGUOUS` and formulate a clear `clarification_needed` question for the user.
6.  **Construct Final JSON**: After completing the analysis, assemble the final `List[ParsedIntent]` JSON object. DO NOT include your thought process in the final JSON output.

**Pydantic Schema for the final output:**
```python
class ParsedIntent(BaseModel):
    intent_type: Literal["CREATE_ORDER", "CLOSE_ORDER", "MODIFY_ORDER", "QUERY_STATUS", "AMBIGUOUS"]
    raw_text: str = Field(..., description="The original text snippet from the input that led to this intent.")
    side: Optional[Literal["buy", "sell"]] = Field(None, description="The determined trade direction. Null if cannot be determined.")
    symbol: Optional[str] = Field(None, description="e.g., 'BTC/USDT'")
    quantity_usd: Optional[float] = Field(None, description="The amount in USD.")
    target_criteria: Optional[str] = Field(None, description="Criteria to select an order, e.g., 'the profitable one'.")
    confidence: float = Field(1.0, description="Your confidence score in this interpretation, from 0.0 to 1.0.")
    clarification_needed: Optional[str] = Field(None, description="If intent is AMBIGUOUS or side is null, explain why and ask a clear question.")
```

---

**Example Execution:**

**User Input to process:** `eth 搞一下，然后把盈利的btc平了`

**Your Internal Thought Process (DO NOT OUTPUT THIS PART):**
1.  **Analyze Keywords**: "eth", "搞一下", "盈利的", "btc", "平了". "搞一下" is ambiguous. "平了" means close. "盈利的" is a condition.
2.  **Deconstruct into Intents**:
    - Intent 1: "eth 搞一下"
    - Intent 2: "把盈利的btc平了"
3.  **Determine Intent Type and Side**:
    - Intent 1: The action "搞一下" is unclear. It could be buy or sell. This is `AMBIGUOUS`. `side` must be `null`.
    - Intent 2: "平了" means `CLOSE_ORDER`. Closing a profitable BTC position usually implies it was a long position. So the closing `side` is `sell`.
4.  **Extract Parameters**:
    - Intent 1: `symbol` is 'ETH/USDT'. No quantity.
    - Intent 2: `symbol` is 'BTC/USDT'. `target_criteria` is "盈利的" (profitable).
5.  **Assess Confidence and Ambiguity**:
    - Intent 1: Low confidence due to "搞一下". Needs clarification.
    - Intent 2: High confidence. The intent is clear.
6.  **Construct Final JSON**: Now, I will construct the JSON based on the analysis above.

---

**Final JSON Output (ONLY THIS PART IS YOUR RESPONSE):**
```json
[
  {
    "intent_type": "AMBIGUOUS",
    "raw_text": "eth 搞一下",
    "side": null,
    "symbol": "ETH/USDT",
    "quantity_usd": null,
    "quantity_percentage": null,
    "target_criteria": null,
    "confidence": 0.4,
    "clarification_needed": "无法确定'eth 搞一下'的具体操作。请问您是想做多还是做空ETH？"
  },
  {
    "intent_type": "CLOSE_ORDER",
    "raw_text": "把盈利的btc平了",
    "side": "sell",
    "symbol": "BTC/USDT",
    "quantity_usd": null,
    "quantity_percentage": null,
    "target_criteria": "the profitable btc positions",
    "confidence": 0.95,
    "clarification_needed": null
  }
]
```

**Instructions & Examples:**

1. **Explicit `buy` (Chinese):**

   - Input: `做多 BTC/USDT，100U`
   - Expected Output:
     JSON
     ```
     [
       {
         "intent_type": "CREATE_ORDER",
         "raw_text": "做多 BTC/USDT，100U",
         "side": "buy",
         "symbol": "BTC/USDT",
         "quantity_usd": 100.0,
         "target_criteria": null,
         "confidence": 0.99,
         "clarification_needed": null
       }
     ]
     ```

2. **Explicit `sell` (English):**

   - Input: `I want to short ETH with 50 dollars`
   - Expected Output:
     JSON
     ```
     [
       {
         "intent_type": "CREATE_ORDER",
         "raw_text": "short ETH with 50 dollars",
         "side": "sell",
         "symbol": "ETH/USDT",
         "quantity_usd": 50.0,
         "target_criteria": null,
         "confidence": 0.98,
         "clarification_needed": null
       }
     ]
     ```

3. **Implicit `buy` (Price-based):**

   - Input: `btc 68000 sl 67000`
   - Expected Output:
     JSON
     ```
     [
       {
         "intent_type": "CREATE_ORDER",
         "raw_text": "btc 68000",
         "side": "buy",
         "symbol": "BTC/USDT",
         "quantity_usd": null,
         "target_criteria": null,
         "confidence": 0.90,
         "clarification_needed": null
       },
       {
         "intent_type": "MODIFY_ORDER",
         "raw_text": "sl 67000",
         "side": null,
         "symbol": "BTC/USDT",
         "quantity_usd": null,
         "target_criteria": "The order for 'btc 68000'",
         "confidence": 0.90,
         "clarification_needed": null
       }
     ]
     ```

4. **Implicit `sell` (Close Position):**

   - Input: `把 btc 平了`
   - Expected Output:
     JSON
     ```
     [
       {
         "intent_type": "CLOSE_ORDER",
         "raw_text": "把 btc 平了",
         "side": "sell",
         "symbol": "BTC/USDT",
         "quantity_usd": null,
         "target_criteria": "all active btc positions",
         "confidence": 0.95,
         "clarification_needed": "Assuming you are closing a long position. If you want to close a short position, please specify."
       }
     ]
     ```

5. **Uncertain Direction (`side: null`):**

   - Input: `eth 搞一下`
   - Expected Output:
     JSON
     ```
     [
       {
         "intent_type": "CREATE_ORDER",
         "raw_text": "eth 搞一下",
         "side": null,
         "symbol": "ETH/USDT",
         "quantity_usd": null,
         "target_criteria": null,
         "confidence": 0.6,
         "clarification_needed": "无法确定 'eth 搞一下' 的交易方向。请问您是想做多还是做空？"
       }
     ]
     ```

6. **Query (Non-trading):**

   - Input: `我的仓位怎么样了`
   - Expected Output:
     JSON
     ```
     [
       {
         "intent_type": "QUERY_STATUS",
         "raw_text": "我的仓位怎么样了",
         "side": null,
         "symbol": null,
         "quantity_usd": null,
         "target_criteria": "all",
         "confidence": 0.99,
         "clarification_needed": null
       }
     ]
     ```

------

User Input to process:

"""

{raw_input}

"""
````

#### **节点 I (分析错误) Prompt**

````text
You are an expert system for analyzing API error messages from cryptocurrency exchanges.
Your task is to classify the given error message and determine if the operation can be retried.
You MUST respond ONLY with a valid JSON object that conforms to the provided Pydantic schema.

**Pydantic Schema:**
class ErrorAnalysis(BaseModel):
    is_correctable: bool = Field(..., description="True if the error is temporary and can be retried, False otherwise.")
    reason: str = Field(..., description="A brief, human-readable explanation of the error.")
    suggestion: str = Field(..., description="A suggestion for the system or user.")

**Instructions & Examples:**

1.  **Correctable Error (Timeout):**
    - Input: `{'error': 'Request Timeout', 'code': 504}`
    - Expected Output:
      ```json
      {
        "is_correctable": true,
        "reason": "网络请求超时。这通常是临时的网络问题或交易所服务器繁忙。",
        "suggestion": "系统将自动进行重试。"
      }
      ```

2.  **Fatal Error (Insufficient Funds):**
    - Input: `ccxt.InsufficientFunds: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}`
    - Expected Output:
      ```json
      {
        "is_correctable": false,
        "reason": "API密钥无效或权限不足。可能是IP地址未加入白名单，或API密钥未开启交易权限。",
        "suggestion": "请检查您的交易所API配置，确认IP白名单和API权限设置正确。任务将终止。"
      }
      ```

**Error Message to analyze:**
"""
{error_message}
"""
````

## 附录 D: 需求覆盖矩阵

需求覆盖矩阵保持不变，所有功能点均已在本设计中得到覆盖。

|             |                          |                                                                                            |          |
| ----------- | ------------------------ | ------------------------------------------------------------------------------------------ | -------- |
| **需求 ID** | **需求描述**             | **设计文档对应章节/方案**                                                                  | **状态** |
| FN-01       | 新建标准订单             | 3.2 (标准流程), 3.3 (节点 D 逻辑), Apx A (`CREATE_ORDER`意图)                              | ✅ 覆盖  |
| FN-02       | 单订单模糊平仓           | 3.4 (`find_orders_by_criteria`), 3.3 (节点 D 逻辑)                                         | ✅ 覆盖  |
| FN-03       | 多订单智能平仓           | 3.4 (`find_orders_by_criteria`), 3.3 (节点 D 逻辑)                                         | ✅ 覆盖  |
| FN-04       | 多订单模糊平仓 (AI 澄清) | 3.2 (J.请求用户确认 节点), 3.3 (节点 J 逻辑), 5 (PENDING_ACTIONS 表), 6.2 (WebSocket 事件) | ✅ 覆盖  |
| FN-05       | 修改订单参数             | Apx A (`MODIFY_ORDER`意图), 3.3 (节点 D 逻辑)                                              | ✅ 覆盖  |
| FN-06       | 复合指令处理             | 3.3 (节点 B 逻辑, 可解析出多个意图), 3.3 (节点 D 逻辑, 可生成多个计划)                     | ✅ 覆盖  |
| FN-07       | 自然语言指代             | 3.4 (`get_message_history`), 3.3 (节点 C 逻辑)                                             | ✅ 覆盖  |
| FN-08       | 批量操作                 | 3.3 (节点 D 逻辑, 可生成多个计划), 3.3 (节点 F 逻辑, 支持多计划执行)                       | ✅ 覆盖  |
| FN-09       | 条件指令                 | 2.1 (`Price Watcher`), 3.4 (`create_conditional_order`), 5 (`CONDITIONAL_ORDERS`表)        | ✅ 覆盖  |
| FN-10       | 参数继承与补充           | 3.3 (节点 D 逻辑, 需实现继承逻辑), 3.4 (`find_orders_by_criteria`)                         | ✅ 覆盖  |
| FN-11       | 查询状态                 | Apx A (`QUERY_STATUS`意图), 对应工具待实现 (e.g., `format_status_response`)                | ✅ 覆盖  |