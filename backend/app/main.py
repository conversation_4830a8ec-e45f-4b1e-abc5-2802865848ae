import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import structlog
from fastapi import (
    BackgroundTasks,
    Depends,
    FastAPI,
    HTTPException,
    WebSocket,
    WebSocketDisconnect,
    status,
)
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.exceptions import HTTPException as StarletteHTTPException

from .agent.graph import run_agent
from .api.v1 import api_router
from .core.auth import get_current_user
from .core.database import get_db
from .core.exceptions import (
    AuthenticationException,
    AuthorizationException,
    BusinessException,
    ExternalServiceException,
    RateLimitException,
    ResourceNotFoundException,
    ValidationException,
    authentication_exception_handler,
    authorization_exception_handler,
    business_exception_handler,
    external_service_exception_handler,
    general_exception_handler,
    http_exception_handler,
    rate_limit_exception_handler,
    resource_not_found_exception_handler,
    validation_error_handler,
    validation_exception_handler,
)
from .core.middleware import ErrorHandlingMiddleware, RequestLoggingMiddleware
from .core.models import User
from .core.schemas import AgentState
from .core.ws_events import EventType, ws_event_manager
from .core.ws_manager import (
    WebSocketManager,
    ws_manager,
)
from .core.ws_schemas import (
    AgentStateTransitionEvent,
    OrderUpdateEvent,
    PendingActionRequiredEvent,
    WebSocketMessage,
)

# 配置结构化日志
from .core.logging import configure_logging_from_settings

from contextlib import asynccontextmanager

from .core.config import get_settings

# 初始化日志系统
settings = get_settings()
logger = configure_logging_from_settings(settings)

# 导入Discord配置管理器
from .services.discord_config_manager import discord_config_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化操作
    logger.info("应用启动中...")

    # 启动Discord配置管理器（如果配置了自动启动）
    if settings.discord.auto_start:
        try:
            await discord_config_manager.start()
            logger.info("Discord配置管理器已启动")
        except Exception as e:
            logger.error("Discord配置管理器启动失败", error=str(e))
    else:
        logger.info("Discord配置管理器未配置自动启动")

    logger.info("应用启动完成")

    yield  # 应用运行期间

    # 关闭时的清理操作
    logger.info("应用关闭中...")

    # 停止Discord配置管理器
    try:
        await discord_config_manager.stop()
        logger.info("Discord配置管理器已停止")
    except Exception as e:
        logger.error("Discord配置管理器停止失败", error=str(e))

    logger.info("应用关闭完成")


# 初始化FastAPI应用
app = FastAPI(
    title="AI Agent 驱动的加密货币智能跟单系统",
    description="基于LangGraph的加密货币智能跟单系统",
    version="0.1.0",
    lifespan=lifespan,
)

# 注册异常处理器
app.add_exception_handler(BusinessException, business_exception_handler)
app.add_exception_handler(ValidationException, validation_exception_handler)
app.add_exception_handler(AuthenticationException, authentication_exception_handler)
app.add_exception_handler(AuthorizationException, authorization_exception_handler)
app.add_exception_handler(
    ResourceNotFoundException, resource_not_found_exception_handler
)
app.add_exception_handler(RateLimitException, rate_limit_exception_handler)
app.add_exception_handler(ExternalServiceException, external_service_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_error_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,  # 使用配置文件中的CORS origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加统一错误处理中间件
app.add_middleware(ErrorHandlingMiddleware)

# 添加请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# 注册API路由
app.include_router(api_router)

# WebSocket连接管理器
# ws_manager 已经在 ws_manager.py 中定义


# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: Optional[str] = None):
    """
    增强的WebSocket连接端点

    Args:
        websocket: WebSocket连接
        token: JWT令牌（可选）
    """
    connection_id = None
    user = None

    try:
        # 验证用户身份
        if token:
            try:
                from .core.auth import AuthManager

                payload = AuthManager.verify_token(token, "access")
                user_id = payload.get("sub")

                if user_id:
                    # 获取用户信息
                    async for db in get_db():
                        from sqlalchemy import select

                        from .core.models import User

                        query = select(User).where(User.id == int(user_id))
                        result = await db.execute(query)
                        user = result.scalar_one_or_none()
                        break

            except Exception as e:
                logger.warning("WebSocket authentication failed", error=str(e))

        if not user:
            # 创建匿名用户（用于测试）
            from .core.models import User

            user = User(id=0, username="anonymous")

        # 建立连接
        connection_id = await ws_manager.connect(websocket, user)

        # 发送连接成功消息
        await websocket.send_text(
            json.dumps(
                {
                    "event_type": "CONNECTION_ESTABLISHED",
                    "payload": {
                        "connection_id": connection_id,
                        "user_id": user.id,
                        "username": user.username,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                }
            )
        )

        # 保持连接并处理消息
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)

                logger.debug(
                    "WebSocket message received",
                    connection_id=connection_id,
                    message_type=message.get("event_type"),
                )

                # 处理不同类型的消息
                await handle_websocket_message(websocket, message, connection_id, user)

            except WebSocketDisconnect:
                logger.info("WebSocket disconnected", connection_id=connection_id)
                break
            except json.JSONDecodeError:
                logger.error("Invalid JSON received", connection_id=connection_id)
                await websocket.send_text(
                    json.dumps(
                        {
                            "event_type": "ERROR",
                            "payload": {
                                "message": "Invalid JSON format",
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                            },
                        }
                    )
                )
            except Exception as e:
                logger.error(
                    "WebSocket message handling error",
                    connection_id=connection_id,
                    error=str(e),
                )
                await websocket.send_text(
                    json.dumps(
                        {
                            "event_type": "ERROR",
                            "payload": {
                                "message": "Internal server error",
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                            },
                        }
                    )
                )

    except Exception as e:
        logger.error("WebSocket connection error", error=str(e))
    finally:
        # 清理连接
        if connection_id:
            await ws_manager.disconnect(connection_id)


async def handle_websocket_message(
    websocket: WebSocket,
    message: Dict[str, Any],
    connection_id: str,
    user: User,
):
    """
    处理WebSocket消息

    Args:
        websocket: WebSocket连接
        message: 消息内容
        connection_id: 连接ID
        user: 用户对象
    """
    event_type = message.get("event_type")
    payload = message.get("payload", {})

    # 检查event_type是否有效
    if not event_type:
        logger.warning(
            "WebSocket message missing event_type",
            connection_id=connection_id,
            message=message,
        )
        await websocket.send_text(
            json.dumps(
                {
                    "event_type": "ERROR",
                    "payload": {
                        "message": "Missing required field: event_type",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                }
            )
        )
        return

    try:
        if event_type == "PING":
            # 心跳响应
            await websocket.send_text(
                json.dumps(
                    {
                        "event_type": "PONG",
                        "payload": {
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        },
                    }
                )
            )
            await ws_manager.handle_pong(connection_id)

        elif event_type == "MESSAGE_ACK":
            # 消息确认
            message_id = payload.get("message_id")
            if message_id:
                await ws_manager.handle_message_ack(connection_id, message_id)

        elif event_type == "SUBSCRIBE":
            # 事件订阅
            event_types = payload.get("event_types", [])
            if event_types:
                from .core.ws_events import EventType, event_bus

                try:
                    enum_types = [EventType(et) for et in event_types]
                    event_bus.subscribe_user(user.id, enum_types)

                    await websocket.send_text(
                        json.dumps(
                            {
                                "event_type": "SUBSCRIPTION_CONFIRMED",
                                "payload": {
                                    "event_types": event_types,
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                },
                            }
                        )
                    )
                except ValueError as e:
                    await websocket.send_text(
                        json.dumps(
                            {
                                "event_type": "ERROR",
                                "payload": {
                                    "message": f"Invalid event type: {str(e)}",
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                },
                            }
                        )
                    )

        elif event_type == "UNSUBSCRIBE":
            # 取消事件订阅
            event_types = payload.get("event_types", [])
            if event_types:
                from .core.ws_events import EventType, event_bus

                try:
                    enum_types = [EventType(et) for et in event_types]
                    event_bus.unsubscribe_user(user.id, enum_types)

                    await websocket.send_text(
                        json.dumps(
                            {
                                "event_type": "UNSUBSCRIPTION_CONFIRMED",
                                "payload": {
                                    "event_types": event_types,
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                },
                            }
                        )
                    )
                except ValueError as e:
                    await websocket.send_text(
                        json.dumps(
                            {
                                "event_type": "ERROR",
                                "payload": {
                                    "message": f"Invalid event type: {str(e)}",
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                },
                            }
                        )
                    )

        elif event_type == "AGENT_REQUEST":
            # Agent请求处理
            await handle_agent_request_enhanced(websocket, payload, user)

        else:
            # 处理无效的event_type
            event_type_str = str(event_type) if event_type is not None else "null"
            logger.warning(
                "Unknown WebSocket event type",
                event_type=event_type,
                connection_id=connection_id,
            )
            await websocket.send_text(
                json.dumps(
                    {
                        "event_type": "ERROR",
                        "payload": {
                            "message": f"Unknown event type: {event_type_str}",
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    }
                )
            )

    except Exception as e:
        logger.error(
            "WebSocket message handling error",
            connection_id=connection_id,
            event_type=event_type,
            error=str(e),
        )
        await websocket.send_text(
            json.dumps(
                {
                    "event_type": "ERROR",
                    "payload": {
                        "message": "Internal server error",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                }
            )
        )


async def handle_agent_request_enhanced(
    websocket: WebSocket, payload: Dict[str, Any], user: User
):
    """
    处理增强的Agent请求

    Args:
        websocket: WebSocket连接
        payload: 请求载荷
        user: 用户对象
    """
    try:
        raw_input = payload.get("text", "")
        if not raw_input:
            await websocket.send_text(
                json.dumps(
                    {
                        "event_type": "ERROR",
                        "payload": {
                            "message": "Missing required field: text",
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    }
                )
            )
            return

        # 创建任务ID
        task_id = uuid.uuid4()

        # 发送任务开始通知
        await ws_event_manager.send_notification(
            {
                "title": "任务开始",
                "message": f"开始处理您的交易请求: {raw_input[:50]}...",
                "level": "info",
                "category": "trading",
                "related_task_id": str(task_id),
            },
            user.id,
        )

        # 创建Agent状态
        state = AgentState(task_id=task_id, user_id=user.id, raw_input=raw_input)

        # 在后台运行Agent
        asyncio.create_task(run_agent_with_events(state, user.id))

        # 发送任务接受确认
        await websocket.send_text(
            json.dumps(
                {
                    "event_type": "AGENT_REQUEST_ACCEPTED",
                    "payload": {
                        "task_id": str(task_id),
                        "status": "processing",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                }
            )
        )

    except Exception as e:
        logger.error("Agent request handling error", user_id=user.id, error=str(e))
        await websocket.send_text(
            json.dumps(
                {
                    "event_type": "ERROR",
                    "payload": {
                        "message": "Failed to process agent request",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                }
            )
        )


async def run_agent_with_events(state: AgentState, user_id: int):
    """
    运行Agent并发送事件

    Args:
        state: Agent状态
        user_id: 用户ID
    """
    try:

        async def node_change_callback(
            from_node: str, to_node: str, current_state: AgentState
        ):
            """节点变更回调"""
            await ws_event_manager.send_agent_state_transition(
                {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "from_node": from_node,
                    "to_node": to_node,
                    "task_id": str(current_state.task_id),
                    "progress_percentage": None,
                    "estimated_completion_time": None,
                    "current_step_description": f"正在执行: {to_node}",
                },
                user_id,
            )

        # 运行Agent
        async with get_db() as db:
            result = await run_agent(state, db, on_node_change=node_change_callback)

            # 发送完成通知
            final_state = result.get("state")
            if final_state and final_state.final_result:
                await ws_event_manager.send_notification(
                    {
                        "title": "任务完成",
                        "message": "您的交易请求已处理完成",
                        "level": "success",
                        "category": "trading",
                        "related_task_id": str(state.task_id),
                    },
                    user_id,
                )
            else:
                await ws_event_manager.send_notification(
                    {
                        "title": "任务失败",
                        "message": "交易请求处理失败，请重试",
                        "level": "error",
                        "category": "trading",
                        "related_task_id": str(state.task_id),
                    },
                    user_id,
                )

    except Exception as e:
        logger.error("Agent execution error", task_id=str(state.task_id), error=str(e))
        await ws_event_manager.send_notification(
            {
                "title": "系统错误",
                "message": "系统处理出现错误，请稍后重试",
                "level": "error",
                "category": "system",
                "related_task_id": str(state.task_id),
            },
            user_id,
        )


# API路由: 处理交易信号
@app.post("/api/v1/process_signal")
async def process_signal(
    signal: dict,
    user_id: int,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
):
    """
    处理交易信号
    """
    task_id = uuid.uuid4()
    logger.info("Processing signal", user_id=user_id, task_id=str(task_id))

    # 创建初始状态
    state = AgentState(
        task_id=task_id, user_id=user_id, raw_input=signal.get("text", "")
    )

    # 在后台任务中运行Agent
    background_tasks.add_task(process_signal_task, state, db)

    return {
        "task_id": str(task_id),
        "status": "accepted",
        "message": "Signal is being processed",
    }


# 后台任务处理函数
async def process_signal_task(state: AgentState, db: AsyncSession):
    """
    在后台处理信号的任务

    全程负责Agent的执行，并通过WebSocket实时推送状态变更

    Args:
        state: Agent初始状态
        db: 数据库会话
    """
    try:
        # 发送Agent开始处理的通知
        from .core.ws_schemas import AgentStateTransitionPayload

        event = AgentStateTransitionEvent(
            payload=AgentStateTransitionPayload(
                timestamp=datetime.now(timezone.utc).isoformat(),
                from_node="Entry",
                to_node="Preprocess",
                task_id=state.task_id,
            )
        )
        await ws_manager.send_message(state.user_id, event.dict())

        # 定义节点变更回调函数
        async def on_node_change(current_node: str, next_node: str, state: AgentState):
            """当Agent的执行状态发生变化时被调用"""
            # 推送状态变更通知
            from .core.ws_schemas import AgentStateTransitionPayload

            event = AgentStateTransitionEvent(
                payload=AgentStateTransitionPayload(
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    from_node=current_node,
                    to_node=next_node,
                    task_id=state.task_id,
                )
            )
            await ws_manager.send_message(state.user_id, event.dict())

            # 如果节点是终态节点，发送结果通知
            if next_node in ["Success", "Failure"]:
                status = "success" if next_node == "Success" else "failure"

                # 发送任务完成通知
                from .core.ws_schemas import TaskCompletedEvent

                event = TaskCompletedEvent(
                    payload={
                        "task_id": str(state.task_id),
                        "status": status,
                        "message": f"任务 {state.task_id} 已完成，状态: {status}",
                    }
                )
                await ws_manager.send_message(state.user_id, event.dict())

                # 如果有订单创建成功，推送订单更新
                if status == "success" and state.final_result:
                    # 理想情况下应该查询数据库获取完整的订单信息
                    # 这里简化为直接推送成功消息
                    from .core.ws_schemas import OrderUpdateEvent

                    # 这里应该传递实际的订单对象，暂时使用简化版本
                    event = WebSocketMessage(
                        event_type="ORDER_UPDATE",
                        payload={
                            "task_id": str(state.task_id),
                            "order_count": sum(
                                1 for r in state.final_result if r.status == "success"
                            ),
                        },
                    )
                    await ws_manager.send_message(state.user_id, event.dict())

        # 运行Agent，传入节点变更回调
        result = await run_agent(state, db, on_node_change)

        # 如果是待处理动作，发送用户确认请求
        if result and result["state"].pending_action_id:
            final_state = result["state"]

            # 查询待处理动作详情
            from sqlalchemy import select

            from .core.models import PendingAction

            query = select(PendingAction).where(
                PendingAction.id == final_state.pending_action_id
            )
            db_result = await db.execute(query)
            pending_action = db_result.scalar_one_or_none()

            if pending_action:
                # 发送待处理动作通知
                from .core.ws_schemas import (
                    PendingActionDetails,
                    PendingActionPayload,
                )

                event = PendingActionRequiredEvent(
                    payload=PendingActionPayload(
                        action_id=pending_action.id,
                        details=PendingActionDetails(
                            raw_input=pending_action.details.get("raw_input", ""),
                            clarification_needed=pending_action.details.get(
                                "clarification_needed", ""
                            ),
                            proposed_plan=pending_action.details.get(
                                "proposed_plan", None
                            ),
                        ),
                        expires_at=pending_action.expires_at.isoformat(),
                    )
                )
                await ws_manager.send_message(state.user_id, event.dict())

                # 设置超时任务
                timeout_seconds = (
                    pending_action.expires_at - datetime.now(timezone.utc)
                ).total_seconds()
                if timeout_seconds > 0:
                    asyncio.create_task(
                        handle_pending_action_timeout(
                            pending_action.id,
                            state.user_id,
                            db,
                            timeout_seconds,
                        )
                    )
    except Exception as e:
        logger.exception(
            "Signal processing failed",
            user_id=state.user_id,
            task_id=str(state.task_id),
            error=str(e),
        )

        # 发送错误通知
        from .core.ws_schemas import NotificationEvent, NotificationPayload

        event = NotificationEvent(
            payload=NotificationPayload(
                title="任务执行失败",
                message=f"任务 {state.task_id} 执行失败: {str(e)}",
                level="error",
                category="system",
                action_required=True,
            )
        )
        await ws_manager.send_message(state.user_id, event.dict())


async def handle_pending_action_timeout(
    action_id: uuid.UUID,
    user_id: int,
    db: AsyncSession,
    timeout_seconds: float,
):
    """
    处理待处理动作的超时

    Args:
        action_id: 动作ID
        user_id: 用户ID
        db: 数据库会话
        timeout_seconds: 超时秒数
    """
    # 等待超时
    await asyncio.sleep(timeout_seconds)

    # 查询动作状态
    from sqlalchemy import select, update

    from .core.models import PendingAction

    query = select(PendingAction).where(PendingAction.id == action_id)
    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    # 如果动作仍处于PENDING状态，则标记为EXPIRED
    if pending_action and pending_action.status == "PENDING":
        await db.execute(
            update(PendingAction)
            .where(PendingAction.id == action_id)
            .values(status="EXPIRED")
        )
        await db.commit()

        # 发送超时通知
        from .core.ws_manager import ws_manager
        from .core.ws_schemas import NotificationEvent, NotificationPayload

        event = NotificationEvent(
            payload=NotificationPayload(
                title="待处理动作已过期",
                message=f"动作 {action_id} 因超时已过期",
                level="warning",
                category="system",
            )
        )
        await ws_manager.send_message(user_id, event.dict())
