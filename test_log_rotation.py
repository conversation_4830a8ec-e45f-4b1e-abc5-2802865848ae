#!/usr/bin/env python3
"""
测试日志轮转功能
生成大量日志来触发文件轮转
"""

import requests
import time
import os

def test_log_rotation():
    """测试日志轮转功能"""
    print("开始测试日志轮转功能...")
    
    # 检查初始日志文件大小
    if os.path.exists("logs/app.log"):
        initial_size = os.path.getsize("logs/app.log")
        print(f"初始日志文件大小: {initial_size} bytes")
    else:
        print("日志文件不存在")
        return
    
    # 生成大量API请求来产生日志
    print("生成API请求...")
    for i in range(100):
        try:
            response = requests.get("http://localhost:8000/api/v1/health", timeout=1)
            if i % 10 == 0:
                print(f"已发送 {i+1} 个请求")
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 短暂延迟避免过快请求
        time.sleep(0.1)
    
    # 检查最终日志文件大小
    if os.path.exists("logs/app.log"):
        final_size = os.path.getsize("logs/app.log")
        print(f"最终日志文件大小: {final_size} bytes")
        print(f"增长了: {final_size - initial_size} bytes")
    
    # 检查是否有轮转文件
    log_files = [f for f in os.listdir("logs") if f.startswith("app.log")]
    print(f"日志文件列表: {log_files}")
    
    print("日志轮转测试完成")

if __name__ == "__main__":
    test_log_rotation()
