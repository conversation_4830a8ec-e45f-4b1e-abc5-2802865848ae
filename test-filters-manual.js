/**
 * 手动测试信号筛选器功能
 * 在浏览器控制台中运行此脚本来测试筛选器
 */

// 测试筛选器功能的脚本
async function testSignalFilters() {
  console.log('=== 开始测试信号筛选器功能 ===');
  
  // 等待页面加载完成
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  try {
    // 1. 测试平台筛选器
    console.log('1. 测试平台筛选器...');
    const platformFilter = document.querySelector('[data-testid="platform-filter"]') || 
                          document.querySelector('label:contains("平台")').parentElement.querySelector('input');
    if (platformFilter) {
      platformFilter.click();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const discordOption = Array.from(document.querySelectorAll('.v-list-item')).find(item => 
        item.textContent.includes('Discord')
      );
      if (discordOption) {
        discordOption.click();
        console.log('✅ 平台筛选器测试成功');
      } else {
        console.log('❌ 找不到Discord选项');
      }
    } else {
      console.log('❌ 找不到平台筛选器');
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 测试搜索功能
    console.log('2. 测试搜索功能...');
    const searchInput = document.querySelector('[data-testid="search-input"]') || 
                       document.querySelector('input[placeholder*="搜索"]');
    if (searchInput) {
      searchInput.value = 'BTC';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      const searchBtn = document.querySelector('[data-testid="search-btn"]') || 
                       document.querySelector('button:contains("搜索")');
      if (searchBtn) {
        searchBtn.click();
        console.log('✅ 搜索功能测试成功');
      } else {
        console.log('❌ 找不到搜索按钮');
      }
    } else {
      console.log('❌ 找不到搜索输入框');
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 测试处理状态筛选器
    console.log('3. 测试处理状态筛选器...');
    const statusFilter = document.querySelector('label:contains("处理状态")');
    if (statusFilter) {
      const statusInput = statusFilter.parentElement.querySelector('input');
      if (statusInput) {
        statusInput.click();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const unprocessedOption = Array.from(document.querySelectorAll('.v-list-item')).find(item => 
          item.textContent.includes('未处理')
        );
        if (unprocessedOption) {
          unprocessedOption.click();
          console.log('✅ 处理状态筛选器测试成功');
        } else {
          console.log('❌ 找不到未处理选项');
        }
      }
    } else {
      console.log('❌ 找不到处理状态筛选器');
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 4. 测试快速筛选按钮
    console.log('4. 测试快速筛选按钮...');
    const quickFilters = ['今天', '本周', '未处理', '高置信度'];
    for (const filterText of quickFilters) {
      const filterBtn = Array.from(document.querySelectorAll('.v-chip')).find(chip => 
        chip.textContent.includes(filterText)
      );
      if (filterBtn) {
        filterBtn.click();
        console.log(`✅ ${filterText} 快速筛选测试成功`);
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        console.log(`❌ 找不到 ${filterText} 快速筛选按钮`);
      }
    }
    
    // 5. 测试重置筛选器
    console.log('5. 测试重置筛选器...');
    const clearBtn = document.querySelector('[data-testid="clear-filters-btn"]') || 
                    Array.from(document.querySelectorAll('button')).find(btn => 
                      btn.textContent.includes('重置') || btn.textContent.includes('清除')
                    );
    if (clearBtn) {
      clearBtn.click();
      console.log('✅ 重置筛选器测试成功');
    } else {
      console.log('❌ 找不到重置筛选器按钮');
    }
    
    console.log('=== 筛选器功能测试完成 ===');
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 检查页面元素的函数
function checkPageElements() {
  console.log('=== 检查页面元素 ===');
  
  const elements = {
    '信号视图容器': document.querySelector('[data-testid="signals-view"]') || document.querySelector('.signals-view'),
    '筛选器容器': document.querySelector('[data-testid="signal-filters"]') || document.querySelector('.signal-filters'),
    '平台筛选器': document.querySelector('[data-testid="platform-filter"]'),
    '搜索输入框': document.querySelector('[data-testid="search-input"]'),
    '搜索按钮': document.querySelector('[data-testid="search-btn"]'),
    '重置按钮': document.querySelector('[data-testid="clear-filters-btn"]'),
    '信号列表': document.querySelector('.signals-list') || document.querySelector('[data-testid="signals-list"]')
  };
  
  Object.entries(elements).forEach(([name, element]) => {
    if (element) {
      console.log(`✅ ${name}: 找到`);
    } else {
      console.log(`❌ ${name}: 未找到`);
    }
  });
  
  console.log('=== 页面元素检查完成 ===');
}

// 检查网络请求的函数
function monitorNetworkRequests() {
  console.log('=== 开始监控网络请求 ===');
  
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('/api/v1/signals')) {
      console.log('🌐 API请求:', url);
    }
    return originalFetch.apply(this, args);
  };
  
  console.log('网络请求监控已启用');
}

// 导出函数供控制台使用
window.testSignalFilters = testSignalFilters;
window.checkPageElements = checkPageElements;
window.monitorNetworkRequests = monitorNetworkRequests;

console.log('筛选器测试脚本已加载！');
console.log('使用方法:');
console.log('1. checkPageElements() - 检查页面元素');
console.log('2. monitorNetworkRequests() - 监控网络请求');
console.log('3. testSignalFilters() - 运行完整测试');
