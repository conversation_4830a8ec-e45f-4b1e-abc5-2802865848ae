<template>
  <v-card
    class="llm-config-card"
    elevation="2"
    color="surface-variant"
  >
    <!-- 卡片头部 -->
    <v-card-title class="d-flex align-center justify-space-between pa-4">
      <div class="d-flex align-center">
        <v-icon
          :icon="getLLMProviderIcon(config.provider)"
          :color="getLLMProviderColor(config.provider)"
          size="24"
          class="me-3"
        />
        <div>
          <div class="text-h6 font-weight-medium">
            {{ config.config_name }}
          </div>
          <div class="text-caption text-medium-emphasis">
            {{ getLLMProviderDisplayName(config.provider) }} • {{ config.model_name }}
          </div>
        </div>
      </div>

      <!-- 状态标签 -->
      <div class="d-flex align-center gap-2">
        <v-chip
          v-if="config.is_default"
          color="primary"
          size="small"
          variant="flat"
        >
          <v-icon start icon="mdi-star" />
          默认
        </v-chip>
        <v-chip
          :color="config.enabled ? 'success' : 'surface'"
          :variant="config.enabled ? 'flat' : 'outlined'"
          size="small"
        >
          <v-icon
            start
            :icon="config.enabled ? 'mdi-check-circle' : 'mdi-pause-circle'"
          />
          {{ config.enabled ? '已启用' : '已禁用' }}
        </v-chip>
      </div>
    </v-card-title>

    <!-- 卡片内容 -->
    <v-card-text class="pa-4 pt-0">
      <!-- 配置参数 -->
      <div class="config-params mb-4">
        <v-row dense>
          <v-col cols="6">
            <div class="text-caption text-medium-emphasis">最大Token</div>
            <div class="text-body-2 font-weight-medium">{{ config.max_tokens.toLocaleString() }}</div>
          </v-col>
          <v-col cols="6">
            <div class="text-caption text-medium-emphasis">温度</div>
            <div class="text-body-2 font-weight-medium">{{ config.temperature }}</div>
          </v-col>
          <v-col cols="6">
            <div class="text-caption text-medium-emphasis">超时时间</div>
            <div class="text-body-2 font-weight-medium">{{ config.timeout_seconds }}秒</div>
          </v-col>
          <v-col cols="6">
            <div class="text-caption text-medium-emphasis">重试次数</div>
            <div class="text-body-2 font-weight-medium">{{ config.max_retries }}次</div>
          </v-col>
        </v-row>
      </div>

      <!-- API密钥 -->
      <div class="api-key-section mb-4">
        <div class="text-caption text-medium-emphasis mb-1">API密钥</div>
        <div class="d-flex align-center">
          <v-chip
            size="small"
            variant="outlined"
            color="surface"
            class="font-mono"
          >
            {{ config.api_key_masked }}
          </v-chip>
        </div>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result mb-4">
        <v-alert
          :type="testResult.success ? 'success' : 'error'"
          variant="tonal"
          density="compact"
          class="text-caption"
        >
          <template #prepend>
            <v-icon 
              :icon="testResult.success ? 'mdi-check-circle' : 'mdi-alert-circle'" 
              size="16"
            />
          </template>
          <div v-if="testResult.success">
            测试成功
            <span v-if="testResult.response_time_ms" class="text-medium-emphasis">
              ({{ testResult.response_time_ms }}ms)
            </span>
          </div>
          <div v-else>
            测试失败: {{ testResult.error_message }}
          </div>
        </v-alert>
      </div>

      <!-- 时间信息 -->
      <div class="time-info text-caption text-medium-emphasis">
        <div>创建时间: {{ formatDateTime(config.created_at) }}</div>
        <div v-if="config.updated_at !== config.created_at">
          更新时间: {{ formatDateTime(config.updated_at) }}
        </div>
      </div>
    </v-card-text>

    <!-- 卡片操作 -->
    <v-card-actions class="pa-4 pt-0">
      <v-btn
        variant="text"
        size="small"
        :loading="testing"
        @click="handleTest"
      >
        <v-icon start icon="mdi-test-tube" />
        测试连接
      </v-btn>

      <v-btn
        variant="text"
        size="small"
        @click="handleEdit"
      >
        <v-icon start icon="mdi-pencil" />
        编辑
      </v-btn>

      <v-spacer />

      <!-- 更多操作菜单 -->
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            variant="text"
            size="small"
            icon="mdi-dots-vertical"
            v-bind="props"
          />
        </template>
        <v-list density="compact">
          <v-list-item
            v-if="!config.is_default"
            @click="handleSetDefault"
          >
            <template #prepend>
              <v-icon icon="mdi-star" />
            </template>
            <v-list-item-title>设为默认</v-list-item-title>
          </v-list-item>

          <v-list-item @click="handleToggle">
            <template #prepend>
              <v-icon 
                :icon="config.enabled ? 'mdi-pause' : 'mdi-play'" 
              />
            </template>
            <v-list-item-title>
              {{ config.enabled ? '禁用' : '启用' }}
            </v-list-item-title>
          </v-list-item>

          <v-list-item @click="handleDuplicate">
            <template #prepend>
              <v-icon icon="mdi-content-copy" />
            </template>
            <v-list-item-title>复制</v-list-item-title>
          </v-list-item>

          <v-divider />

          <v-list-item
            @click="handleDelete"
            class="text-error"
          >
            <template #prepend>
              <v-icon icon="mdi-delete" />
            </template>
            <v-list-item-title>删除</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-card-actions>
  </v-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { LLMConfig } from '@/types/llm.types'
import {
  getLLMProviderIcon,
  getLLMProviderColor,
  getLLMProviderDisplayName
} from '@/types/llm.types'
import { useLLMConfigStore } from '@/stores/llmConfig'

// Props
interface Props {
  config: LLMConfig
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'edit', config: LLMConfig): void
  (e: 'delete', config: LLMConfig): void
  (e: 'duplicate', config: LLMConfig): void
}

const emit = defineEmits<Emits>()

// Store
const llmConfigStore = useLLMConfigStore()

// 状态
const testing = ref(false)

// 计算属性
const testResult = computed(() => 
  llmConfigStore.getTestResult(props.config.id)
)

// 方法
function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

async function handleTest() {
  testing.value = true
  try {
    await llmConfigStore.testConfig(props.config.id)
  } finally {
    testing.value = false
  }
}

function handleEdit() {
  emit('edit', props.config)
}

async function handleSetDefault() {
  try {
    await llmConfigStore.setDefaultConfig(props.config.id)
  } catch (error) {
    // 错误已在store中处理
  }
}

async function handleToggle() {
  try {
    await llmConfigStore.toggleConfig(props.config.id)
  } catch (error) {
    // 错误已在store中处理
  }
}

function handleDuplicate() {
  emit('duplicate', props.config)
}

function handleDelete() {
  emit('delete', props.config)
}
</script>

<style scoped>
.llm-config-card {
  margin-bottom: 16px;
}

.config-params .v-col {
  padding: 4px 8px;
}

.font-mono {
  font-family: 'Courier New', monospace;
}

.test-result {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
