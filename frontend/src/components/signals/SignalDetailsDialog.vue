<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="800"
    scrollable
    persistent
    data-testid="signal-details-dialog"
  >
    <v-card v-if="signal">
      <!-- 对话框头部 -->
      <v-card-title class="d-flex align-center">
        <v-icon left color="primary">mdi-signal</v-icon>
        <span>信号详情</span>
        <v-spacer />

        <!-- 平台标识 -->
        <v-chip
          :color="getPlatformColor(signal.platform)"
          variant="outlined"
          size="small"
          class="mr-2"
        >
          <v-icon left size="small">{{ getPlatformIcon(signal.platform) }}</v-icon>
          {{ getPlatformName(signal.platform) }}
        </v-chip>

        <!-- 处理状态 -->
        <v-chip
          :color="signal.is_processed ? 'success' : 'warning'"
          variant="outlined"
          size="small"
          class="mr-2"
        >
          <v-icon left size="small">
            {{ signal.is_processed ? 'mdi-check-circle' : 'mdi-clock-outline' }}
          </v-icon>
          {{ signal.is_processed ? '已处理' : '未处理' }}
        </v-chip>

        <v-btn
          icon="mdi-close"
          variant="text"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text class="pa-0">
        <v-container fluid>
          <v-row>
            <!-- 左侧：基本信息 -->
            <v-col cols="12" md="8">
              <v-card variant="outlined" class="mb-4">
                <v-card-title class="text-h6">基本信息</v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" sm="6">
                      <v-text-field
                        label="信号ID"
                        :model-value="signal.id"
                        readonly
                        variant="outlined"
                        density="compact"
                      />
                    </v-col>
                    <v-col cols="12" sm="6">
                      <v-text-field
                        label="平台"
                        :model-value="getPlatformName(signal.platform)"
                        readonly
                        variant="outlined"
                        density="compact"
                      />
                    </v-col>
                    <v-col cols="12" sm="6">
                      <v-text-field
                        label="频道名称"
                        :model-value="signal.channel_name || '-'"
                        readonly
                        variant="outlined"
                        density="compact"
                      />
                    </v-col>
                    <v-col cols="12" sm="6">
                      <v-text-field
                        label="作者"
                        :model-value="signal.author_name || '-'"
                        readonly
                        variant="outlined"
                        density="compact"
                      />
                    </v-col>
                    <v-col cols="12" sm="6">
                      <v-text-field
                        label="创建时间"
                        :model-value="formatDateTime(signal.created_at)"
                        readonly
                        variant="outlined"
                        density="compact"
                      />
                    </v-col>
                    <v-col cols="12" sm="6">
                      <v-text-field
                        label="处理时间"
                        :model-value="signal.processed_at ? formatDateTime(signal.processed_at) : '-'"
                        readonly
                        variant="outlined"
                        density="compact"
                      />
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>

              <!-- 信号内容 -->
              <v-card variant="outlined" class="mb-4">
                <v-card-title class="text-h6">
                  信号内容
                  <v-spacer />
                  <v-btn-toggle
                    v-model="contentViewMode"
                    mandatory
                    variant="outlined"
                    size="small"
                  >
                    <v-btn value="processed" size="small">
                      <v-icon size="small">mdi-format-text</v-icon>
                      处理后
                    </v-btn>
                    <v-btn v-if="signal.platform === 'discord'" value="discord" size="small">
                      <v-icon size="small">mdi-discord</v-icon>
                      Discord样式
                    </v-btn>
                    <v-btn value="raw" size="small">
                      <v-icon size="small">mdi-code-tags</v-icon>
                      原始内容
                    </v-btn>
                  </v-btn-toggle>
                </v-card-title>
                <v-card-text>
                  <!-- 处理后内容 -->
                  <div v-if="contentViewMode === 'processed'" class="content-display">
                    <pre class="content-text" data-testid="signal-content">{{ signal.content }}</pre>
                  </div>

                  <!-- Discord样式渲染 -->
                  <div v-else-if="contentViewMode === 'discord'" class="discord-display" data-testid="discord-embeds">
                    <DiscordMessage
                      :signal="signal"
                      :initial-render-mode="'discord'"
                    />
                  </div>

                  <!-- 原始内容 -->
                  <div v-else class="content-display">
                    <pre class="content-text">{{ signal.raw_content || signal.content }}</pre>
                  </div>
                </v-card-text>
              </v-card>

              <!-- 元数据 -->
              <v-card v-if="signal.metadata && Object.keys(signal.metadata).length > 0" variant="outlined">
                <v-card-title class="text-h6">
                  元数据
                  <v-spacer />
                  <v-btn
                    size="small"
                    variant="outlined"
                    @click="copyMetadata"
                  >
                    <v-icon left size="small">mdi-content-copy</v-icon>
                    复制
                  </v-btn>
                </v-card-title>
                <v-card-text>
                  <div class="metadata-viewer">
                    <pre class="json-content">{{ formatJSON(signal.metadata) }}</pre>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 右侧：操作面板 -->
            <v-col cols="12" md="4">
              <v-card variant="outlined" class="mb-4">
                <v-card-title class="text-h6">信号强度</v-card-title>
                <v-card-text>
                  <div class="mb-4">
                    <div class="d-flex align-center justify-space-between mb-2">
                      <span class="text-body-2">当前强度</span>
                      <span class="text-h6 font-weight-bold">
                        {{ signal.signal_strength ? (signal.signal_strength * 100).toFixed(0) + '%' : '-' }}
                      </span>
                    </div>
                    <v-progress-linear
                      :model-value="(signal.signal_strength || 0) * 100"
                      :color="getStrengthColor(signal.signal_strength || 0)"
                      height="8"
                      rounded
                    />
                  </div>

                  <v-slider
                    v-model="editableStrength"
                    :min="0"
                    :max="100"
                    :step="5"
                    thumb-label="always"
                    color="primary"
                    track-color="grey-lighten-3"
                    :disabled="updating"
                  >
                    <template v-slot:thumb-label="{ modelValue }">
                      {{ modelValue }}%
                    </template>
                  </v-slider>

                  <v-btn
                    block
                    color="primary"
                    @click="updateStrength"
                    :loading="updating"
                    :disabled="editableStrength === (signal.signal_strength || 0) * 100"
                  >
                    <v-icon left>mdi-content-save</v-icon>
                    更新强度
                  </v-btn>
                </v-card-text>
              </v-card>

              <!-- 状态操作 -->
              <v-card variant="outlined" class="mb-4">
                <v-card-title class="text-h6">状态操作</v-card-title>
                <v-card-text>
                  <v-btn
                    block
                    :color="signal.is_processed ? 'warning' : 'success'"
                    @click="toggleProcessed"
                    :loading="updating"
                    class="mb-3"
                    data-testid="toggle-processed-btn"
                  >
                    <v-icon left>
                      {{ signal.is_processed ? 'mdi-undo' : 'mdi-check' }}
                    </v-icon>
                    {{ signal.is_processed ? '标记为未处理' : '标记为已处理' }}
                  </v-btn>

                  <v-btn
                    block
                    color="error"
                    variant="outlined"
                    @click="showDeleteConfirm = true"
                    :loading="updating"
                    data-testid="delete-signal-btn"
                  >
                    <v-icon left>mdi-delete</v-icon>
                    删除信号
                  </v-btn>
                </v-card-text>
              </v-card>

              <!-- 技术信息 -->
              <v-card variant="outlined">
                <v-card-title class="text-h6">技术信息</v-card-title>
                <v-card-text>
                  <v-list density="compact">
                    <v-list-item>
                      <v-list-item-title class="text-caption">平台消息ID</v-list-item-title>
                      <v-list-item-subtitle>{{ signal.platform_message_id || '-' }}</v-list-item-subtitle>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title class="text-caption">频道ID</v-list-item-title>
                      <v-list-item-subtitle>{{ signal.channel_id || '-' }}</v-list-item-subtitle>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title class="text-caption">作者ID</v-list-item-title>
                      <v-list-item-subtitle>{{ signal.author_id || '-' }}</v-list-item-subtitle>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title class="text-caption">消息类型</v-list-item-title>
                      <v-list-item-subtitle>{{ signal.message_type || 'text' }}</v-list-item-subtitle>
                    </v-list-item>
                  </v-list>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <!-- 对话框底部 -->
      <v-card-actions>
        <v-spacer />
        <v-btn
          variant="text"
          @click="closeDialog"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- 删除确认对话框 -->
    <v-dialog v-model="showDeleteConfirm" max-width="400" data-testid="delete-confirm-dialog">
      <v-card>
        <v-card-title class="text-h6">确认删除</v-card-title>
        <v-card-text>
          确定要删除这个信号吗？此操作不可撤销。
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn variant="text" @click="showDeleteConfirm = false">取消</v-btn>
          <v-btn
            color="error"
            @click="deleteSignal"
            :loading="updating"
            data-testid="confirm-delete-btn"
          >
            删除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useSnackbar } from '@/composables/useSnackbar'
import { signalApi } from '@/api/signals'
import DiscordMessage from './DiscordMessage.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  signal: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'update', 'close'])

// 组合式API
const { showSnackbar } = useSnackbar()

// 响应式数据
const updating = ref(false)
const contentViewMode = ref('processed')
const editableStrength = ref(0)
const showDeleteConfirm = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const getPlatformName = (platform) => {
  const names = {
    discord: 'Discord',
    telegram: 'Telegram',
    manual: '手动'
  }
  return names[platform] || platform
}

const getStrengthColor = (strength) => {
  if (strength >= 0.8) return 'success'
  if (strength >= 0.6) return 'warning'
  if (strength >= 0.4) return 'orange'
  return 'error'
}

const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatJSON = (obj) => {
  return JSON.stringify(obj, null, 2)
}

const copyMetadata = async () => {
  try {
    await navigator.clipboard.writeText(formatJSON(props.signal.metadata))
    showSnackbar('元数据已复制到剪贴板', 'success')
  } catch (error) {
    console.error('复制失败:', error)
    showSnackbar('复制失败', 'error')
  }
}

const updateStrength = async () => {
  try {
    updating.value = true

    const response = await signalApi.updateSignal(props.signal.id, {
      signal_strength: editableStrength.value / 100
    })

    if (response.success) {
      emit('update', response.data)
      showSnackbar('信号强度更新成功', 'success')
    } else {
      showSnackbar(response.message || '更新失败', 'error')
    }
  } catch (error) {
    console.error('更新信号强度失败:', error)
    showSnackbar('更新信号强度失败', 'error')
  } finally {
    updating.value = false
  }
}

const toggleProcessed = async () => {
  try {
    updating.value = true

    const response = await signalApi.updateSignal(props.signal.id, {
      is_processed: !props.signal.is_processed
    })

    if (response.success) {
      emit('update', response.data)
      showSnackbar(
        `信号已${props.signal.is_processed ? '标记为未处理' : '标记为已处理'}`,
        'success'
      )
    } else {
      showSnackbar(response.message || '更新失败', 'error')
    }
  } catch (error) {
    console.error('更新信号状态失败:', error)
    showSnackbar('更新信号状态失败', 'error')
  } finally {
    updating.value = false
  }
}

const deleteSignal = async () => {
  try {
    updating.value = true

    const response = await signalApi.deleteSignal(props.signal.id)

    if (response.success) {
      showSnackbar('信号删除成功', 'success')
      showDeleteConfirm.value = false
      closeDialog()
      emit('update', null) // 触发列表刷新
    } else {
      showSnackbar(response.message || '删除失败', 'error')
    }
  } catch (error) {
    console.error('删除信号失败:', error)
    showSnackbar('删除信号失败', 'error')
  } finally {
    updating.value = false
  }
}

const closeDialog = () => {
  dialogVisible.value = false
  emit('close')
}

// 监听信号变化，更新可编辑强度
watch(
  () => props.signal,
  (newSignal) => {
    if (newSignal) {
      editableStrength.value = (newSignal.signal_strength || 0) * 100
    }
  },
  { immediate: true }
)

// 监听对话框关闭，重置状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      contentViewMode.value = 'processed'
      showDeleteConfirm.value = false
    }
  }
)
</script>

<style scoped>
.v-dialog .v-card {
  max-height: 90vh;
}

.content-display {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.02);
}

.content-text {
  padding: 16px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Roboto Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.metadata-viewer {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.02);
}

.json-content {
  padding: 16px;
  margin: 0;
  font-family: 'Roboto Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #2e7d32;
}

.v-progress-linear {
  border-radius: 4px;
}

.v-slider {
  margin: 16px 0;
}

.v-list-item {
  padding: 8px 0;
}

.v-list-item-title {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
}

.v-list-item-subtitle {
  font-family: 'Roboto Mono', monospace;
  font-size: 12px;
  word-break: break-all;
}

/* Discord显示样式 */
.discord-display {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.02);
}

.discord-display >>> .discord-message {
  margin: 0;
  border-radius: 0;
}

.discord-display >>> .render-toggle {
  display: none;
}

/* 响应式设计 */
@media (max-width: 960px) {
  .v-dialog .v-card {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }

  .content-display,
  .metadata-viewer,
  .discord-display {
    max-height: 200px;
  }

  .v-container {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .v-card-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .v-card-title .v-spacer {
    display: none;
  }

  .content-text,
  .json-content {
    font-size: 12px;
    padding: 12px;
  }
}

/* 滚动条样式 */
.content-display::-webkit-scrollbar,
.metadata-viewer::-webkit-scrollbar {
  width: 6px;
}

.content-display::-webkit-scrollbar-track,
.metadata-viewer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.content-display::-webkit-scrollbar-thumb,
.metadata-viewer::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.content-display::-webkit-scrollbar-thumb:hover,
.metadata-viewer::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 卡片间距 */
.v-card.mb-4 {
  margin-bottom: 16px !important;
}

/* 按钮样式 */
.v-btn.mb-3 {
  margin-bottom: 12px !important;
}
</style>