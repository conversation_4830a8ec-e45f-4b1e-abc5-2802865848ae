/**
 * 测试信号技术信息显示功能
 * 在浏览器控制台中运行此脚本来验证技术信息字段是否正确显示
 */

// 测试技术信息显示的脚本
async function testTechnicalInfoDisplay() {
  console.log('=== 开始测试信号技术信息显示 ===');
  
  try {
    // 等待页面加载完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 1. 查找信号列表中的第一个信号
    console.log('1. 查找信号列表...');
    const signalRows = document.querySelectorAll('.v-data-table tbody tr');
    if (signalRows.length === 0) {
      console.log('❌ 没有找到信号列表');
      return;
    }
    
    console.log(`✅ 找到 ${signalRows.length} 条信号记录`);
    
    // 2. 点击第一个信号打开详情
    console.log('2. 点击第一个信号打开详情...');
    const firstRow = signalRows[0];
    firstRow.click();
    
    // 等待详情对话框打开
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 3. 检查详情对话框是否打开
    const detailDialog = document.querySelector('[data-testid="signal-details-dialog"]') ||
                        document.querySelector('.v-dialog--active');
    
    if (!detailDialog) {
      console.log('❌ 详情对话框未打开');
      return;
    }
    
    console.log('✅ 详情对话框已打开');
    
    // 4. 检查技术信息部分
    console.log('3. 检查技术信息部分...');
    const technicalInfoSection = Array.from(document.querySelectorAll('.v-card-title')).find(title => 
      title.textContent.includes('技术信息')
    );
    
    if (!technicalInfoSection) {
      console.log('❌ 找不到技术信息部分');
      return;
    }
    
    console.log('✅ 找到技术信息部分');
    
    // 5. 检查各个技术信息字段
    const technicalInfoCard = technicalInfoSection.closest('.v-card');
    const listItems = technicalInfoCard.querySelectorAll('.v-list-item');
    
    const technicalFields = {
      '平台消息ID': null,
      '频道ID': null,
      '作者ID': null,
      '消息类型': null
    };
    
    listItems.forEach(item => {
      const title = item.querySelector('.v-list-item-title');
      const subtitle = item.querySelector('.v-list-item-subtitle');
      
      if (title && subtitle) {
        const fieldName = title.textContent.trim();
        const fieldValue = subtitle.textContent.trim();
        
        if (technicalFields.hasOwnProperty(fieldName)) {
          technicalFields[fieldName] = fieldValue;
          
          if (fieldValue && fieldValue !== '-') {
            console.log(`✅ ${fieldName}: ${fieldValue}`);
          } else {
            console.log(`⚠️ ${fieldName}: 无值 (${fieldValue})`);
          }
        }
      }
    });
    
    // 6. 统计结果
    const fieldsWithValues = Object.entries(technicalFields).filter(([key, value]) => 
      value && value !== '-'
    ).length;
    
    const totalFields = Object.keys(technicalFields).length;
    
    console.log(`\n=== 技术信息字段统计 ===`);
    console.log(`有值字段: ${fieldsWithValues}/${totalFields}`);
    console.log(`字段详情:`, technicalFields);
    
    if (fieldsWithValues >= 3) {
      console.log('✅ 技术信息显示正常');
    } else if (fieldsWithValues >= 1) {
      console.log('⚠️ 部分技术信息显示正常');
    } else {
      console.log('❌ 技术信息显示异常');
    }
    
    // 7. 关闭对话框
    console.log('4. 关闭详情对话框...');
    const closeBtn = detailDialog.querySelector('.v-btn[aria-label="close"]') ||
                    detailDialog.querySelector('.mdi-close').closest('.v-btn') ||
                    Array.from(detailDialog.querySelectorAll('.v-btn')).find(btn => 
                      btn.textContent.includes('关闭') || btn.textContent.includes('取消')
                    );
    
    if (closeBtn) {
      closeBtn.click();
      console.log('✅ 对话框已关闭');
    } else {
      console.log('⚠️ 未找到关闭按钮，请手动关闭');
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
  
  console.log('=== 技术信息显示测试完成 ===');
}

// 检查网络请求中的技术信息
function monitorTechnicalInfoRequests() {
  console.log('=== 开始监控技术信息相关的网络请求 ===');
  
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('/api/v1/signals/') && !url.includes('?')) {
      console.log('🌐 信号详情API请求:', url);
      
      return originalFetch.apply(this, args).then(response => {
        if (response.ok) {
          // 克隆响应以便检查内容
          const clonedResponse = response.clone();
          clonedResponse.json().then(data => {
            if (data.success && data.data) {
              const technicalInfo = {
                platform_message_id: data.data.platform_message_id,
                channel_id: data.data.channel_id,
                author_id: data.data.author_id,
                message_type: data.data.message_type
              };
              console.log('📊 API返回的技术信息:', technicalInfo);
              
              const hasValues = Object.values(technicalInfo).some(value => value && value !== null);
              if (hasValues) {
                console.log('✅ API返回了技术信息');
              } else {
                console.log('❌ API未返回技术信息');
              }
            }
          }).catch(err => {
            console.log('⚠️ 解析API响应失败:', err);
          });
        }
        return response;
      });
    }
    return originalFetch.apply(this, args);
  };
  
  console.log('网络请求监控已启用');
}

// 导出函数供控制台使用
window.testTechnicalInfoDisplay = testTechnicalInfoDisplay;
window.monitorTechnicalInfoRequests = monitorTechnicalInfoRequests;

console.log('技术信息测试脚本已加载！');
console.log('使用方法:');
console.log('1. monitorTechnicalInfoRequests() - 监控网络请求');
console.log('2. testTechnicalInfoDisplay() - 测试技术信息显示');
