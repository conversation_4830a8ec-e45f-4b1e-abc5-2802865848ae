"""
Agent服务 - 处理AI Agent相关的业务逻辑
"""
import asyncio
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import structlog
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from ..agent.graph import build_agent_graph
from ..core.models import Agent<PERSON>he<PERSON><PERSON>, PendingAction, User
from ..core.schemas import (
    AgentState,
    ProcessSignalRequest,
    TaskCreatedResponse,
)

logger = structlog.get_logger()


class AgentService:
    """Agent服务类"""

    def __init__(self, session_factory=None):
        self.agent_graph = None
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._session_factory = session_factory

    async def get_agent_graph(self, db: AsyncSession):
        """获取Agent图实例"""
        if self.agent_graph is None:
            self.agent_graph = build_agent_graph(db)
        return self.agent_graph

    async def process_signal(
        self, request: ProcessSignalRequest, user: User, db: AsyncSession
    ) -> TaskCreatedResponse:
        """
        处理交易信号

        Args:
            request: 处理请求
            user: 用户对象
            db: 数据库会话

        Returns:
            TaskCreatedResponse: 任务创建响应
        """
        task_id = str(uuid.uuid4())

        # 创建初始状态
        import uuid as uuid_module

        initial_state = AgentState(
            task_id=uuid_module.UUID(task_id),  # 明确设置task_id，避免自动生成
            user_id=user.id,
            raw_input=request.raw_input,
            context=request.context or {},
        )

        try:
            # 获取Agent图
            graph = await self.get_agent_graph(db)

            # 启动异步任务处理
            task = asyncio.create_task(
                self._process_agent_task(task_id, initial_state, db)
            )
            self._running_tasks[task_id] = task

            logger.info(
                "Agent任务已创建",
                task_id=task_id,
                user_id=user.id,
                raw_input=request.raw_input,
            )

            return TaskCreatedResponse(
                task_id=task_id, status="processing", message="任务已创建，正在处理中"
            )

        except Exception as e:
            logger.error("创建Agent任务失败", task_id=task_id, user_id=user.id, error=str(e))
            raise

    async def _process_agent_task(
        self, task_id: str, initial_state: AgentState, db: AsyncSession
    ):
        """
        处理Agent任务的内部方法

        Args:
            task_id: 任务ID
            initial_state: 初始状态
            db: 数据库会话
        """
        # 为后台任务创建新的数据库会话
        if self._session_factory:
            async_session = self._session_factory()
        else:
            from ..core.database import AsyncSessionLocal

            async_session = AsyncSessionLocal()

        try:
            # 获取Agent图
            graph = await self.get_agent_graph(async_session)

            # 执行Agent工作流
            from ..agent.graph import run_agent

            result = await run_agent(initial_state, async_session)

            # 提取AgentState对象
            if isinstance(result, dict) and "state" in result:
                final_state = result["state"]
            else:
                final_state = result

            # 保存最终结果
            await self._save_final_result(task_id, final_state, async_session)

            logger.info(
                "Agent任务完成",
                task_id=task_id,
                user_id=initial_state.user_id,
                status="success",
            )

        except Exception as e:
            logger.error(
                "Agent任务执行失败",
                task_id=task_id,
                user_id=initial_state.user_id,
                error=str(e),
            )

            # 保存错误结果
            await self._save_error_result(task_id, str(e), async_session)

        finally:
            # 关闭数据库会话
            await async_session.close()
            # 清理任务
            if task_id in self._running_tasks:
                del self._running_tasks[task_id]

    async def _save_final_result(
        self, task_id: str, result: AgentState, db: AsyncSession
    ):
        """保存最终结果"""
        try:
            # 使用检查点管理器的序列化功能
            from ..agent.checkpoint import CheckpointManager

            checkpoint_manager = CheckpointManager(db)

            # 保存最终检查点
            await checkpoint_manager.save_checkpoint(result, "Final")

        except Exception as e:
            logger.error("保存Agent结果失败", task_id=task_id, error=str(e))
            await db.rollback()

    async def _save_error_result(
        self, task_id: str, error_message: str, db: AsyncSession
    ):
        """保存错误结果"""
        try:
            # 创建错误检查点
            checkpoint = AgentCheckpoint(
                task_id=task_id,
                user_id=0,  # 未知用户
                node_name="Error",
                state_data={
                    "error": error_message,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

            db.add(checkpoint)
            await db.commit()

        except Exception as e:
            logger.error("保存Agent错误失败", task_id=task_id, error=str(e))
            await db.rollback()

    async def get_task_status(
        self, task_id: str, db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            task_id: 任务ID
            db: 数据库会话

        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        try:
            # 将字符串task_id转换为UUID
            import uuid as uuid_module

            try:
                task_uuid = uuid_module.UUID(task_id)
            except ValueError:
                logger.warning("Invalid task_id format", task_id=task_id)
                return None

            # 查询最新的检查点
            stmt = (
                select(AgentCheckpoint)
                .where(AgentCheckpoint.task_id == task_uuid)
                .order_by(AgentCheckpoint.created_at.desc())
                .limit(1)
            )

            result = await db.execute(stmt)
            checkpoint = result.scalar_one_or_none()

            if not checkpoint:
                # 如果没有找到检查点，检查任务是否在运行中
                is_running = task_id in self._running_tasks
                if is_running:
                    return {
                        "task_id": task_id,
                        "status": "running",
                        "current_node": "Processing",
                        "last_update": None,
                        "state_data": {},
                    }
                return None

            # 检查任务是否还在运行
            is_running = task_id in self._running_tasks

            # 确定任务状态
            if is_running:
                status = "running"
            else:
                # 检查是否失败 - 首先检查状态数据中的status字段
                if checkpoint.state_data and isinstance(checkpoint.state_data, dict):
                    # 检查状态数据中的status字段
                    state_status = checkpoint.state_data.get("status")
                    if state_status == "failed":
                        status = "failed"
                    elif checkpoint.node_name in [
                        "Failure",
                        "Failed",
                        "Error",
                    ]:
                        status = "failed"
                    else:
                        # 检查其他失败标识
                        final_result = checkpoint.state_data.get("final_result")
                        if final_result and isinstance(final_result, dict):
                            if final_result.get("success") is False:
                                status = "failed"
                            else:
                                status = "completed"
                        else:
                            status = "completed"
                elif checkpoint.node_name in ["Failure", "Failed", "Error"]:
                    status = "failed"
                else:
                    status = "completed"

            return {
                "task_id": task_id,
                "status": status,
                "current_node": checkpoint.node_name,
                "last_update": checkpoint.created_at.isoformat(),
                "state_data": checkpoint.state_data,
            }

        except Exception as e:
            logger.error("获取任务状态失败", task_id=task_id, error=str(e))
            return None

    async def list_user_tasks(
        self, user_id: int, db: AsyncSession, limit: int = 20, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        列出用户的任务

        Args:
            user_id: 用户ID
            db: 数据库会话
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            # 查询用户的检查点，按任务分组
            stmt = (
                select(AgentCheckpoint)
                .where(AgentCheckpoint.user_id == user_id)
                .order_by(AgentCheckpoint.created_at.desc())
                .limit(limit)
                .offset(offset)
            )

            result = await db.execute(stmt)
            checkpoints = result.scalars().all()

            # 按任务ID分组
            tasks = {}
            for checkpoint in checkpoints:
                task_id = checkpoint.task_id
                if task_id not in tasks:
                    tasks[task_id] = {
                        "task_id": task_id,
                        "status": "running"
                        if task_id in self._running_tasks
                        else "completed",
                        "created_at": checkpoint.created_at.isoformat(),
                        "last_node": checkpoint.node_name,
                        "state_data": checkpoint.state_data,
                    }

            return list(tasks.values())

        except Exception as e:
            logger.error("列出用户任务失败", user_id=user_id, error=str(e))
            return []

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            task.cancel()
            del self._running_tasks[task_id]

            logger.info("任务已取消", task_id=task_id)
            return True

        return False

    def get_running_tasks_count(self) -> int:
        """获取正在运行的任务数量"""
        return len(self._running_tasks)


# 全局Agent服务实例
agent_service = AgentService()
