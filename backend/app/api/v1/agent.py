"""
Agent API endpoints for task management and processing
"""
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth import get_current_user
from app.core.database import get_db
from app.core.models import User
from app.core.schemas import (
    AgentCheckpoint,
    AgentTask,
    APIResponse,
    PaginatedResponse,
    ProcessSignalRequest,
    TaskCreatedResponse,
)
from app.services.agent_service import AgentService

router = APIRouter(prefix="/agent", tags=["agent"])


@router.get("/status/{task_id}", response_model=APIResponse[dict])
async def get_task_status(
    task_id: str,  # 改为str类型
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取指定任务的状态或最新检查点"""
    from app.services.agent_service import agent_service

    try:
        status = await agent_service.get_task_status(task_id, db)
        if not status:
            raise HTTPException(status_code=404, detail="Task not found")

        return APIResponse(status="success", data=status)
    except HTTPException:
        # 重新抛出HTTP异常，保持原始状态码
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/process", response_model=APIResponse[TaskCreatedResponse])
async def process_signal(
    request: ProcessSignalRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """提交新的交易指令处理"""
    from app.services.agent_service import agent_service

    try:
        # 使用实际的AgentService方法
        response = await agent_service.process_signal(request, current_user, db)

        return APIResponse(status="success", data=response)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cancel/{task_id}", response_model=APIResponse[None])
async def cancel_task(
    task_id: str,  # 改为str类型
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """取消正在执行的任务"""
    from app.services.agent_service import agent_service

    try:
        success = await agent_service.cancel_task(task_id)
        if not success:
            raise HTTPException(
                status_code=404, detail="Task not found or cannot be cancelled"
            )

        return APIResponse(
            status="success", data=None, message="Task cancelled successfully"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_model=APIResponse[List[dict]])
async def get_user_tasks(
    status: Optional[str] = Query(None, description="Filter by task status"),
    limit: int = Query(20, ge=1, le=100, description="Number of tasks per page"),
    offset: int = Query(0, ge=0, description="Number of tasks to skip"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户的任务历史"""
    from app.services.agent_service import agent_service

    try:
        tasks = await agent_service.list_user_tasks(
            user_id=current_user.id, db=db, limit=limit, offset=offset
        )

        return APIResponse(status="success", data=tasks)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=APIResponse[dict])
async def get_agent_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户的Agent使用统计"""
    from app.services.agent_service import agent_service

    try:
        # 简化统计信息
        stats = {
            "running_tasks": agent_service.get_running_tasks_count(),
            "user_id": current_user.id,
        }
        return APIResponse(status="success", data=stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
