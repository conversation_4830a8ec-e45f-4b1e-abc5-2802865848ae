"""
测试配置文件 - 提供全局测试fixtures和配置
"""
import asyncio
import json
import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import AsyncGenerator, Generator
from unittest.mock import Mock, patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import ASGITransport, AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.auth import AuthManager
from app.core.config import settings
# Import app later to avoid circular imports
from app.core.database import Base, get_db
from app.core.models import (AgentCheckpoint, ConditionalOrder, ExchangeConfig,
                             LLMConfig, Order, PendingAction, RiskConfig, User)
from app.core.schemas import (AgentState, IntentType, OrderType, ParsedIntent,
                              TradePlan, TradeSide)

# 测试数据库URL - 使用PostgreSQL
# 在Docker容器内运行时使用postgres服务名，否则使用localhost
import os
if os.getenv('DOCKER_ENV') == 'true' or os.path.exists('/.dockerenv'):
    # 在Docker容器内使用正确的服务名
    TEST_DATABASE_URL = "postgresql+asyncpg://crypto_trader:test_password_123@postgres-test:5432/crypto_trader_test"
else:
    # 在本地开发环境
    TEST_DATABASE_URL = "postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test"


# PostgreSQL测试环境不需要字段修改
def patch_models_for_postgresql():
    """PostgreSQL测试环境，模型字段无需修改"""
    # PostgreSQL原生支持JSONB、ARRAY和UUID字段
    pass


# 创建测试引擎 - PostgreSQL
test_engine = create_async_engine(
    TEST_DATABASE_URL, echo=False
)

# 创建测试会话工厂
TestSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话 - 使用PostgreSQL"""
    # PostgreSQL测试环境，模型无需修改
    patch_models_for_postgresql()

    # 使用共享的PostgreSQL测试数据库
    # 每个测试在事务中运行，测试结束后回滚

    # 创建独立的测试引擎
    from sqlalchemy.ext.asyncio import create_async_engine

    isolated_engine = create_async_engine(TEST_DATABASE_URL, echo=False)

    # 确保表存在
    async with isolated_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建会话工厂
    from sqlalchemy.orm import sessionmaker

    IsolatedSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=isolated_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    # 创建会话
    async with IsolatedSessionLocal() as session:
        try:
            # 设置Factory的session为异步session（如果存在）
            try:
                from tests.factories import (AgentCheckpointFactory,
                                             ConditionalOrderFactory,
                                             ExchangeConfigFactory,
                                             OrderFactory,
                                             PendingActionFactory,
                                             RiskConfigFactory,
                                             UserFactory)

                # 为所有Factory设置异步session
                for factory_class in [
                    UserFactory,
                    RiskConfigFactory,
                    OrderFactory,
                    ExchangeConfigFactory,
                    ConditionalOrderFactory,
                    PendingActionFactory,
                    AgentCheckpointFactory,
                ]:
                    if hasattr(factory_class, "_meta"):
                        factory_class._meta.sqlalchemy_session = session
            except ImportError:
                # 如果factories不存在，跳过
                pass

            yield session
        finally:
            await session.rollback()
            await session.close()

    # 关闭引擎
    await isolated_engine.dispose()


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    """创建异步数据库会话（别名，用于兼容性）"""
    # 复用db_session的逻辑
    patch_models_for_postgresql()

    # 使用共享的PostgreSQL测试数据库
    from app.core.database import AsyncSessionLocal

    async with AsyncSessionLocal() as session:
        yield session


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """覆盖数据库依赖"""

    async def _override_get_db():
        yield db_session

    return _override_get_db


@pytest.fixture
def client(override_get_db):
    """创建测试客户端"""
    # Import app here to avoid circular imports
    from app.main import app as fastapi_app

    fastapi_app.dependency_overrides[get_db] = override_get_db
    with TestClient(fastapi_app) as test_client:
        yield test_client
    fastapi_app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def async_client(
    override_get_db, db_session
) -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    # Import app here to avoid circular imports
    from app.main import app as fastapi_app

    fastapi_app.dependency_overrides[get_db] = override_get_db

    # 创建测试专用的agent service，使用测试数据库会话
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.orm import sessionmaker

    from app.services.agent_service import AgentService

    # 获取测试数据库引擎
    test_engine = db_session.bind

    # 创建测试会话工厂
    test_session_factory = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    # 创建测试agent service
    test_agent_service = AgentService(session_factory=test_session_factory)

    # 替换全局agent service
    import app.services.agent_service

    original_agent_service = app.services.agent_service.agent_service
    app.services.agent_service.agent_service = test_agent_service

    try:
        async with AsyncClient(
            transport=ASGITransport(app=fastapi_app), base_url="http://test"
        ) as ac:
            yield ac
    finally:
        fastapi_app.dependency_overrides.clear()
        # 恢复原始agent service
        app.services.agent_service.agent_service = original_agent_service


# ============================================================================
# 测试数据工厂
# ============================================================================


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """创建测试用户"""
    import uuid
    unique_id = uuid.uuid4().hex[:8]
    unique_username = f"testuser_{unique_id}"
    unique_email = f"testuser_{unique_id}@example.com"
    user = User(
        username=unique_username,
        email=unique_email,
        password_hash=AuthManager.hash_password("testpassword123"),
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest_asyncio.fixture
async def test_admin_user(db_session: AsyncSession) -> User:
    """创建测试管理员用户"""
    import uuid
    unique_id = uuid.uuid4().hex[:8]
    unique_username = f"admin_{unique_id}"
    unique_email = f"admin_{unique_id}@example.com"
    user = User(
        username=unique_username,
        email=unique_email,
        password_hash=AuthManager.hash_password("adminpassword123")
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest_asyncio.fixture
async def test_risk_config(db_session: AsyncSession, test_user: User) -> RiskConfig:
    """创建测试风控配置"""
    risk_config = RiskConfig(
        user_id=test_user.id,
        max_concurrent_orders=5,
        max_total_position_value_usd=Decimal("1000.0"),
        default_position_size_usd=Decimal("100.0"),
        max_position_size_usd=Decimal("500.0"),
        allowed_symbols=["BTC/USDT", "ETH/USDT"],
        confidence_threshold=Decimal("0.8"),
    )
    db_session.add(risk_config)
    await db_session.commit()
    await db_session.refresh(risk_config)
    return risk_config


@pytest_asyncio.fixture
async def test_order(db_session: AsyncSession, test_user: User) -> Order:
    """创建测试订单"""
    # 不设置id，让数据库自动生成
    order = Order(
        user_id=test_user.id,
        client_order_id=f"test_order_{uuid.uuid4()}",
        symbol="BTC/USDT",
        side="buy",
        quantity=Decimal("0.001"),
        entry_price=Decimal("68000.0"),
        status="active",
    )
    db_session.add(order)
    await db_session.commit()
    await db_session.refresh(order)
    return order


# 删除重复的async_client fixture，使用上面的版本





@pytest_asyncio.fixture
async def sample_agent_state(test_user: User) -> AgentState:
    """创建示例Agent状态"""
    return AgentState(user_id=test_user.id, raw_input="做多 BTC/USDT，100U")


@pytest.fixture
def sample_parsed_intent() -> ParsedIntent:
    """创建示例解析意图"""
    return ParsedIntent(
        intent_type=IntentType.CREATE_ORDER,
        raw_text="做多 BTC/USDT，100U",
        side=TradeSide.BUY,
        symbol="BTC/USDT",
        quantity_usd=Decimal("100.0"),
        confidence=Decimal("0.95"),
    )


@pytest.fixture
def sample_trade_plan() -> TradePlan:
    """创建示例交易计划"""
    return TradePlan(
        symbol="BTC/USDT",
        side=TradeSide.BUY,
        order_type=OrderType.MARKET,
        quantity=Decimal("0.00147"),  # 约100USD at 68000
    )


# ============================================================================
# 认证相关fixtures
# ============================================================================


@pytest_asyncio.fixture
async def auth_headers(test_user: User) -> dict:
    """创建认证头"""
    access_token = AuthManager.create_access_token({"sub": str(test_user.id)})
    return {"Authorization": f"Bearer {access_token}"}


@pytest_asyncio.fixture
async def authenticated_client(
    async_client: AsyncClient, auth_headers: dict
) -> AsyncClient:
    """创建已认证的客户端"""
    async_client.headers.update(auth_headers)
    return async_client


# ============================================================================
# Mock相关fixtures
# ============================================================================


@pytest.fixture
def mock_openai_client(monkeypatch):
    """Mock OpenAI客户端"""

    class MockOpenAIClient:
        async def chat_completions_create(self, **kwargs):
            return {
                "choices": [
                    {
                        "message": {
                            "content": '{"intent_type": "CREATE_ORDER", "side": "buy", "symbol": "BTC/USDT", "quantity_usd": 100.0}'
                        }
                    }
                ]
            }

    mock_client = MockOpenAIClient()
    monkeypatch.setattr("app.agent.nodes.client", mock_client)
    return mock_client


@pytest.fixture
def mock_exchange_service(monkeypatch):
    """Mock交易所服务"""

    class MockExchangeService:
        async def get_market_price(self, symbol: str) -> Decimal:
            prices = {
                "BTC/USDT": Decimal("68000.0"),
                "ETH/USDT": Decimal("3500.0"),
                "SOL/USDT": Decimal("150.0"),
            }
            return prices.get(symbol, Decimal("0.0"))

        async def execute_trade(self, trade_plan: TradePlan):
            return {
                "status": "success",
                "order_id": str(uuid.uuid4()),
                "client_order_id": str(uuid.uuid4()),
            }

    mock_service = MockExchangeService()
    monkeypatch.setattr("app.services.exchange.ExchangeService", lambda: mock_service)
    return mock_service


# ============================================================================
# 测试配置
# ============================================================================


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """设置测试环境"""
    # 强制使用仿真模式
    monkeypatch.setenv("SIMULATION_MODE", "true")
    monkeypatch.setenv("OPENAI_API_KEY", "sk-mock-testing")
    monkeypatch.setenv("DATABASE_URL", TEST_DATABASE_URL)

    # 更新settings
    monkeypatch.setattr(settings, "simulation_mode", True)
    monkeypatch.setattr(settings.llm, "openai_api_key", "sk-mock-testing")


@pytest_asyncio.fixture(autouse=True)
async def cleanup_async_resources():
    """自动清理异步资源，防止pending tasks警告"""
    yield

    # 清理WebSocket管理器
    try:
        from app.core.ws_manager import ws_manager
        await ws_manager.shutdown()
        # 重置WebSocket管理器状态
        ws_manager.connection_map.clear()
        ws_manager.active_connections.clear()
        ws_manager.disconnected_users.clear()
        ws_manager.offline_messages.clear()
        ws_manager.cleanup_task = None
    except Exception:
        # 忽略清理错误，避免影响测试结果
        pass

    # 清理条件订单服务
    try:
        from app.services.conditional_order_service import conditional_order_service
        await conditional_order_service.shutdown()
    except Exception:
        # 忽略清理错误，避免影响测试结果
        pass

    # 等待一小段时间让任务自然完成
    try:
        await asyncio.sleep(0.01)
    except Exception:
        pass

    # 简单等待一下让任务自然完成，避免强制取消导致递归
    try:
        await asyncio.sleep(0.1)
    except Exception:
        pass


# ============================================================================
# 测试标记
# ============================================================================

# 定义测试标记
pytest_plugins = ["pytest_asyncio"]


def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "e2e: 端到端测试")
    config.addinivalue_line("markers", "slow: 慢速测试")
    config.addinivalue_line("markers", "simulation: 仿真模式测试")
    config.addinivalue_line("markers", "discord: Discord相关测试")


# Discord测试相关fixtures
@pytest.fixture
def mock_discord_settings():
    """模拟Discord设置"""
    import json
    from unittest.mock import Mock

    settings = Mock()
    settings.discord.token = "test_discord_token"
    settings.discord.monitored_channels = json.dumps(
        {
            "123456789": {
                "name": "test-signals",
                "user_id": 1,
                "enabled": True,
                "signal_filters": ["vip", "premium"],
            },
            "987654321": {
                "name": "test-general",
                "user_id": 2,
                "enabled": True,
                "signal_filters": [],
            },
        }
    )
    settings.discord.auto_start = True
    settings.discord.reconnect_attempts = 3
    settings.discord.reconnect_delay = 10
    settings.discord.message_cache_size = 1000
    settings.discord.deduplication_window = 5

    return settings


@pytest.fixture
def mock_discord_message():
    """创建模拟Discord消息的工厂函数"""
    from datetime import datetime, timezone
    from unittest.mock import Mock

    def _create_message(
        content: str = "buy btc",
        message_id: int = 123456789,
        author_id: int = 111222333,
        author_name: str = "TestUser",
        channel_id: int = 123456789,
        channel_name: str = "test-signals",
        is_bot: bool = False,
        has_attachments: bool = False,
        has_embeds: bool = False,
        is_reply: bool = False,
    ):
        message = Mock()
        message.id = message_id
        message.content = content

        # 作者信息
        message.author = Mock()
        message.author.id = author_id
        message.author.display_name = author_name
        message.author.bot = is_bot
        message.author.__str__ = lambda: f"{author_name}#1234"

        # 频道信息
        message.channel = Mock()
        message.channel.id = channel_id
        message.channel.name = channel_name

        # 时间戳
        message.created_at = datetime.now(timezone.utc)

        # 附件和嵌入
        message.attachments = [] if not has_attachments else [Mock()]
        message.embeds = [] if not has_embeds else [Mock()]

        # 回复信息
        if is_reply:
            message.reference = Mock()
            message.reference.message_id = 999888777
        else:
            message.reference = None

        # 提及信息
        message.mentions = []
        message.role_mentions = []
        message.channel_mentions = []

        return message

    return _create_message


@pytest.fixture
def mock_discord_client():
    """模拟Discord客户端"""
    from unittest.mock import AsyncMock, Mock

    from app.services.discord_listener import TradingSignalClient

    with patch("app.services.discord_listener.settings") as mock_settings:
        mock_settings.discord.monitored_channels = json.dumps(
            {
                "123456789": {
                    "name": "test-signals",
                    "user_id": 1,
                    "enabled": True,
                    "signal_filters": ["vip"],
                }
            }
        )
        mock_settings.discord.deduplication_window = 5
        mock_settings.discord.message_cache_size = 1000
        mock_settings.discord.reconnect_attempts = 3
        mock_settings.discord.reconnect_delay = 10

        client = TradingSignalClient()
        client.user = Mock()
        client.user.id = 999888777
        client.process_trading_signal = AsyncMock()

        return client


# LLM配置相关fixtures
@pytest_asyncio.fixture
async def sample_llm_config(async_session: AsyncSession, test_user: User):
    """创建示例LLM配置"""
    config = LLMConfig(
        id=uuid.uuid4(),
        user_id=test_user.id,
        config_name="测试LLM配置",
        provider="chatgpt",
        enabled=True,
        is_default=False,
        api_key="sk-test123456789abcdef",
        api_base_url="https://api.openai.com/v1",
        model_name="gpt-4",
        max_tokens=4096,
        temperature=Decimal("0.7"),
        timeout_seconds=60,
        max_retries=3
    )
    async_session.add(config)
    await async_session.commit()
    await async_session.refresh(config)
    return config


@pytest.fixture
def llm_config_data():
    """LLM配置测试数据"""
    return {
        "config_name": "测试配置",
        "provider": "chatgpt",
        "enabled": True,
        "is_default": False,
        "api_key": "sk-test123456789",
        "api_base_url": "https://api.openai.com/v1",
        "model_name": "gpt-4",
        "max_tokens": 4096,
        "temperature": 0.7,
        "timeout_seconds": 60,
        "max_retries": 3
    }


@pytest.fixture
def multiple_llm_configs_data():
    """多个LLM配置测试数据"""
    return [
        {
            "config_name": "ChatGPT主配置",
            "provider": "chatgpt",
            "enabled": True,
            "is_default": True,
            "api_key": "sk-chatgpt123456789",
            "model_name": "gpt-4",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout_seconds": 60,
            "max_retries": 3
        },
        {
            "config_name": "DeepSeek备用",
            "provider": "deepseek",
            "enabled": False,
            "is_default": False,
            "api_key": "ds-deepseek987654321",
            "model_name": "deepseek-chat",
            "max_tokens": 2048,
            "temperature": 0.8,
            "timeout_seconds": 30,
            "max_retries": 2
        },
        {
            "config_name": "Claude测试",
            "provider": "claude",
            "enabled": True,
            "is_default": False,
            "api_key": "claude-key-abcdef",
            "model_name": "claude-3-sonnet",
            "max_tokens": 8192,
            "temperature": 0.5,
            "timeout_seconds": 90,
            "max_retries": 5
        }
    ]
