/**
 * 信号管理API客户端
 *
 * 提供信号相关的API调用方法，包括：
 * - 获取信号列表（分页、筛选）
 * - 获取单个信号详情
 * - 创建新信号
 * - 更新信号状态
 * - 删除信号
 * - 获取信号统计信息
 */

import { get, post, put, del } from './client.ts'

export const signalApi = {
  /**
   * 获取信号列表（分页）
   * @param {SignalQueryParams} params - 查询参数
   * @param {string} [params.platform] - 平台筛选
   * @param {string} [params.channel_id] - 频道ID筛选
   * @param {boolean} [params.is_processed] - 处理状态筛选
   * @param {number} [params.confidence_min] - 最小置信度
   * @param {number} [params.confidence_max] - 最大置信度
   * @param {string} [params.ai_parse_status] - AI解析状态筛选
   * @param {string} [params.message_type_ai] - AI消息类型筛选
   * @param {string} [params.llm_service] - LLM服务筛选
   * @param {string} [params.date_from] - 开始日期
   * @param {string} [params.date_to] - 结束日期
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.size=20] - 每页大小
   * @param {string} [params.sort_by='created_at'] - 排序字段
   * @param {string} [params.sort_order='desc'] - 排序方向
   * @param {number} [params.signal_strength_min] - 已废弃，请使用confidence_min
   * @returns {Promise<SignalListResponse>} API响应
   */
  async getSignals(params = {}) {
    try {
      const response = await get('/api/v1/signals', params)
      return response
    } catch (error) {
      console.error('获取信号列表失败:', error)
      throw error
    }
  },

  /**
   * 获取单个信号详情
   * @param {string} signalId - 信号ID
   * @returns {Promise<Object>} API响应
   */
  async getSignalDetail(signalId) {
    try {
      const response = await get(`/api/v1/signals/${signalId}`)
      return response
    } catch (error) {
      console.error('获取信号详情失败:', error)
      throw error
    }
  },

  /**
   * 创建新信号
   * @param {Object} signalData - 信号数据
   * @param {string} signalData.platform - 平台类型
   * @param {string} signalData.content - 信号内容
   * @param {string} [signalData.channel_name] - 频道名称
   * @param {string} [signalData.author_name] - 作者名称
   * @param {string} [signalData.raw_content] - 原始内容
   * @param {Object} [signalData.metadata] - 元数据
   * @returns {Promise<Object>} API响应
   */
  async createSignal(signalData) {
    try {
      const response = await post('/api/v1/signals', signalData)
      return response
    } catch (error) {
      console.error('创建信号失败:', error)
      throw error
    }
  },

  /**
   * 更新信号状态
   * @param {string} signalId - 信号ID
   * @param {Object} updateData - 更新数据
   * @param {boolean} [updateData.is_processed] - 是否已处理
   * @param {number} [updateData.signal_strength] - 信号强度
   * @param {Object} [updateData.metadata] - 元数据
   * @returns {Promise<Object>} API响应
   */
  async updateSignal(signalId, updateData) {
    try {
      const response = await put(`/api/v1/signals/${signalId}`, updateData)
      return response
    } catch (error) {
      console.error('更新信号失败:', error)
      throw error
    }
  },

  /**
   * 删除信号
   * @param {string} signalId - 信号ID
   * @returns {Promise<Object>} API响应
   */
  async deleteSignal(signalId) {
    try {
      const response = await del(`/api/v1/signals/${signalId}`)
      return response
    } catch (error) {
      console.error('删除信号失败:', error)
      throw error
    }
  },

  /**
   * 获取信号统计信息
   * @param {number} [days=30] - 统计天数
   * @returns {Promise<Object>} API响应
   */
  async getStats(days = 30) {
    try {
      const response = await get('/api/v1/signals/stats', { days })
      return response
    } catch (error) {
      console.error('获取信号统计失败:', error)
      throw error
    }
  },

  /**
   * 批量更新信号状态
   * @param {Array<string>} signalIds - 信号ID数组
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Array>} 更新结果数组
   */
  async batchUpdateSignals(signalIds, updateData) {
    try {
      const promises = signalIds.map(id => this.updateSignal(id, updateData))
      const results = await Promise.allSettled(promises)

      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length

      return {
        successful,
        failed,
        results: results.map((result, index) => ({
          signalId: signalIds[index],
          success: result.status === 'fulfilled',
          data: result.status === 'fulfilled' ? result.value : null,
          error: result.status === 'rejected' ? result.reason : null
        }))
      }
    } catch (error) {
      console.error('批量更新信号失败:', error)
      throw error
    }
  },

  /**
   * 搜索信号
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} API响应
   */
  async searchSignals(query, options = {}) {
    try {
      const params = {
        search: query,
        page: options.page || 1,
        size: options.size || 20,
        ...options
      }

      const response = await get('/api/v1/signals', params)
      return response
    } catch (error) {
      console.error('搜索信号失败:', error)
      throw error
    }
  }
}