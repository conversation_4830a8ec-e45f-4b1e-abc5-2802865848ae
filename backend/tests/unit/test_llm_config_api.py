"""
LLM配置API单元测试

测试LLM配置相关的API端点功能
"""

import pytest
import uuid
from decimal import Decimal
from unittest.mock import AsyncMock, patch

from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.models import User, LLMConfig
from app.core.schemas import LLMConfigRequest, LLMProvider
from app.api.v1.llm_configs import router


class TestLLMConfigAPI:
    """LLM配置API测试类"""

    @pytest.fixture
    async def sample_user(self, async_session: AsyncSession):
        """创建测试用户"""
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        async_session.add(user)
        await async_session.commit()
        await async_session.refresh(user)
        return user

    @pytest.fixture
    async def sample_llm_config(self, async_session: AsyncSession, sample_user: User):
        """创建测试LLM配置"""
        config = LLMConfig(
            id=uuid.uuid4(),
            user_id=sample_user.id,
            config_name="测试配置",
            provider="chatgpt",
            enabled=True,
            is_default=False,
            api_key="test_api_key_12345",
            model_name="gpt-4",
            max_tokens=4096,
            temperature=Decimal("0.7"),
            timeout_seconds=60,
            max_retries=3
        )
        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)
        return config

    @pytest.fixture
    def sample_config_request(self):
        """创建测试配置请求数据"""
        return LLMConfigRequest(
            config_name="新测试配置",
            provider=LLMProvider.DEEPSEEK,
            enabled=True,
            is_default=False,
            api_key="new_test_api_key",
            model_name="deepseek-chat",
            max_tokens=2048,
            temperature=0.8,
            timeout_seconds=30,
            max_retries=2
        )

    async def test_get_llm_configs_success(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig
    ):
        """测试获取LLM配置列表成功"""
        response = await authenticated_client.get("/api/v1/llm-configs")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 1
        
        config_data = data["data"][0]
        assert config_data["id"] == str(sample_llm_config.id)
        assert config_data["config_name"] == sample_llm_config.config_name
        assert config_data["provider"] == sample_llm_config.provider
        assert "api_key_masked" in config_data
        assert config_data["api_key_masked"] == "test***2345"

    async def test_get_llm_configs_empty(self, authenticated_client, sample_user: User):
        """测试获取空的LLM配置列表"""
        response = await authenticated_client.get("/api/v1/llm-configs")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 0

    async def test_create_llm_config_success(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_config_request: LLMConfigRequest
    ):
        """测试创建LLM配置成功"""
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=sample_config_request.model_dump()
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        
        config_data = data["data"]
        assert config_data["config_name"] == sample_config_request.config_name
        assert config_data["provider"] == sample_config_request.provider.value
        assert config_data["enabled"] == sample_config_request.enabled
        assert config_data["model_name"] == sample_config_request.model_name

    async def test_create_llm_config_duplicate_name(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig,
        sample_config_request: LLMConfigRequest
    ):
        """测试创建重复名称的LLM配置失败"""
        # 使用已存在的配置名称
        sample_config_request.config_name = sample_llm_config.config_name
        
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=sample_config_request.model_dump()
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "配置名称已存在" in data["detail"]

    async def test_create_llm_config_invalid_provider(
        self, 
        authenticated_client, 
        sample_user: User
    ):
        """测试创建无效提供商的LLM配置失败"""
        invalid_request = {
            "config_name": "无效配置",
            "provider": "invalid_provider",
            "enabled": True,
            "is_default": False,
            "api_key": "test_key",
            "model_name": "test_model",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout_seconds": 60,
            "max_retries": 3
        }
        
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=invalid_request
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_update_llm_config_success(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig
    ):
        """测试更新LLM配置成功"""
        update_data = {
            "config_name": "更新后的配置",
            "provider": "claude",
            "enabled": False,
            "is_default": True,
            "api_key": "updated_api_key",
            "model_name": "claude-3-sonnet",
            "max_tokens": 8192,
            "temperature": 0.5,
            "timeout_seconds": 90,
            "max_retries": 5
        }
        
        response = await authenticated_client.put(
            f"/api/v1/llm-configs/{sample_llm_config.id}",
            json=update_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        
        config_data = data["data"]
        assert config_data["config_name"] == update_data["config_name"]
        assert config_data["provider"] == update_data["provider"]
        assert config_data["enabled"] == update_data["enabled"]
        assert config_data["is_default"] == update_data["is_default"]

    async def test_update_llm_config_not_found(
        self, 
        authenticated_client, 
        sample_user: User
    ):
        """测试更新不存在的LLM配置失败"""
        non_existent_id = str(uuid.uuid4())
        update_data = {
            "config_name": "不存在的配置",
            "provider": "chatgpt",
            "enabled": True,
            "is_default": False,
            "api_key": "test_key",
            "model_name": "gpt-4",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout_seconds": 60,
            "max_retries": 3
        }
        
        response = await authenticated_client.put(
            f"/api/v1/llm-configs/{non_existent_id}",
            json=update_data
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    async def test_delete_llm_config_success(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig
    ):
        """测试删除LLM配置成功"""
        response = await authenticated_client.delete(
            f"/api/v1/llm-configs/{sample_llm_config.id}"
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["data"]["deleted_id"] == str(sample_llm_config.id)

    async def test_delete_llm_config_not_found(
        self, 
        authenticated_client, 
        sample_user: User
    ):
        """测试删除不存在的LLM配置失败"""
        non_existent_id = str(uuid.uuid4())
        
        response = await authenticated_client.delete(
            f"/api/v1/llm-configs/{non_existent_id}"
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    async def test_set_default_llm_config_success(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig
    ):
        """测试设置默认LLM配置成功"""
        response = await authenticated_client.post(
            f"/api/v1/llm-configs/{sample_llm_config.id}/set-default"
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["data"]["config_id"] == str(sample_llm_config.id)

    async def test_test_llm_config_success(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig
    ):
        """测试LLM配置连接成功"""
        test_data = {"test_message": "Hello, test!"}
        
        response = await authenticated_client.post(
            f"/api/v1/llm-configs/{sample_llm_config.id}/test",
            json=test_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        
        test_result = data["data"]
        assert "success" in test_result
        assert "response_text" in test_result or "error_message" in test_result

    async def test_unauthorized_access(self, client):
        """测试未授权访问失败"""
        response = await client.get("/api/v1/llm-configs")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.parametrize("invalid_id", [
        "invalid-uuid",
        "12345",
        "",
        "not-a-uuid-at-all"
    ])
    async def test_invalid_config_id_format(
        self, 
        authenticated_client, 
        sample_user: User, 
        invalid_id: str
    ):
        """测试无效的配置ID格式"""
        response = await authenticated_client.get(f"/api/v1/llm-configs/{invalid_id}")
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    async def test_create_default_config_clears_others(
        self, 
        authenticated_client, 
        sample_user: User, 
        sample_llm_config: LLMConfig
    ):
        """测试创建默认配置时清除其他默认配置"""
        # 先设置现有配置为默认
        await authenticated_client.post(
            f"/api/v1/llm-configs/{sample_llm_config.id}/set-default"
        )
        
        # 创建新的默认配置
        new_config_data = {
            "config_name": "新默认配置",
            "provider": "gemini",
            "enabled": True,
            "is_default": True,
            "api_key": "new_default_key",
            "model_name": "gemini-pro",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout_seconds": 60,
            "max_retries": 3
        }
        
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=new_config_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        # 验证只有新配置是默认的
        configs_response = await authenticated_client.get("/api/v1/llm-configs")
        configs = configs_response.json()["data"]
        
        default_configs = [c for c in configs if c["is_default"]]
        assert len(default_configs) == 1
        assert default_configs[0]["config_name"] == "新默认配置"
