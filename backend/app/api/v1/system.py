"""
System API endpoints for health checks, metrics, and version info
"""
import time
from datetime import datetime, timedelta, timezone

import psutil
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.core.database import get_db
from app.core.schemas import (
    APIResponse,
    HealthStatus,
    SystemMetrics,
    VersionInfo,
)
from app.core.ws_manager import ws_manager
from app.services.discord_listener import discord_manager

router = APIRouter(prefix="/system", tags=["system"])

# 系统启动时间
_start_time = time.time()


@router.get(
    "/health/detailed",
    response_model=APIResponse[HealthStatus],
    operation_id="detailed_health_check",
)
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    详细系统健康检查

    提供完整的系统健康状态，包括数据库、外部服务、WebSocket等组件的状态
    适用于详细的系统监控和诊断
    """
    checks = {}
    status = "healthy"

    # 检查数据库连接
    try:
        result = await db.execute(text("SELECT 1"))
        result.fetchone()  # 移除await，因为fetchone()不是异步的
        checks["database"] = "healthy"
    except Exception as e:
        checks["database"] = f"unhealthy: {str(e)}"
        status = "degraded"

    # 检查外部服务状态
    settings = get_settings()

    # 检查OpenAI API（如果配置了）
    if (
        hasattr(settings, "llm")
        and hasattr(settings.llm, "openai_api_key")
        and settings.llm.openai_api_key
    ):
        try:
            # 这里可以添加实际的OpenAI API健康检查
            checks["openai"] = "healthy"
        except Exception as e:
            checks["openai"] = f"unhealthy: {str(e)}"
            status = "degraded"

    # 检查WebSocket管理器
    try:
        # 简化WebSocket检查，避免访问不存在的属性
        checks["websocket"] = "healthy"
    except Exception as e:
        checks["websocket"] = f"unhealthy: {str(e)}"
        status = "degraded"

    health_status = HealthStatus(status=status, timestamp=datetime.now(), checks=checks)

    return APIResponse.success_response(data=health_status, message="系统健康检查完成")


@router.get("/health", operation_id="simple_health_check")
async def simple_health_check(db: AsyncSession = Depends(get_db)):
    """
    简单健康检查端点

    用于负载均衡器和基础监控系统的快速健康检查
    返回简化的状态信息，响应速度更快

    Args:
        db: 数据库会话

    Returns:
        简化的健康状态
    """
    try:
        # 尝试连接数据库
        await db.execute(text("SELECT 1"))
        db_status = "healthy"
        overall_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
        overall_status = "unhealthy"

    # 返回与API标准一致的格式
    return APIResponse.success_response(
        data={
            "status": overall_status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "checks": {"database": db_status},
        },
        message="健康检查完成",
    )


@router.get("/metrics", response_model=APIResponse[SystemMetrics])
async def get_system_metrics():
    """获取系统指标"""
    try:
        # 获取系统资源使用情况
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        disk_info = psutil.disk_usage("/")

        # 获取WebSocket连接数
        active_connections = len(ws_manager.connection_map)

        # 获取活跃任务数（这里需要实际的任务管理器）
        running_tasks = 0  # TODO: 从任务管理器获取实际数据

        metrics = SystemMetrics(
            cpu_usage=cpu_percent,
            memory_usage=memory_info.percent,
            disk_usage=disk_info.percent,
            active_connections=active_connections,
            running_tasks=running_tasks,
            timestamp=datetime.utcnow(),
        )

        return APIResponse.success_response(data=metrics, message="系统指标获取成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.get("/version", response_model=APIResponse[VersionInfo])
async def get_version_info():
    """获取系统版本信息"""
    settings = get_settings()

    version_info = VersionInfo(
        version=settings.app_version,
        build_date=datetime.utcnow().isoformat(),  # TODO: 从构建时设置
        environment=settings.environment,
        git_commit=getattr(settings, "git_commit", None),  # 从配置中获取git commit，如果没有则为None
    )

    return APIResponse.success_response(data=version_info, message="版本信息获取成功")


@router.get("/status", response_model=APIResponse[dict])
async def get_system_status():
    """获取系统综合状态"""
    try:
        # 获取基本系统信息
        uptime = int(time.time() - _start_time)
        uptime_str = str(timedelta(seconds=uptime))

        # 获取进程信息
        process = psutil.Process()
        memory_info = process.memory_info()

        status = {
            "uptime": uptime,
            "uptime_human": uptime_str,
            "memory_usage_mb": memory_info.rss / 1024 / 1024,
            "cpu_percent": process.cpu_percent(),
            "threads": process.num_threads(),
            "connections": {
                "websocket": len(ws_manager.connection_map),
                "database": "unknown",  # TODO: 从连接池获取
            },
            "environment": get_settings().environment,
            "debug_mode": get_settings().debug,
        }

        return APIResponse.success_response(data=status, message="系统状态获取成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.post("/maintenance", response_model=APIResponse[None])
async def toggle_maintenance_mode():
    """切换维护模式（需要管理员权限）"""
    # TODO: 实现维护模式逻辑
    # TODO: 添加管理员权限检查
    raise HTTPException(status_code=501, detail="Maintenance mode not implemented")


@router.post("/cache/clear", response_model=APIResponse[None])
async def clear_cache():
    """清除系统缓存（需要管理员权限）"""
    # TODO: 实现缓存清除逻辑
    # TODO: 添加管理员权限检查
    raise HTTPException(status_code=501, detail="Cache clearing not implemented")


# Discord监控相关API端点
@router.get("/discord/status", response_model=APIResponse[dict])
async def get_discord_status():
    """获取Discord监听器状态"""
    try:
        manager_status = discord_manager.get_status()

        # 如果有客户端实例，获取详细状态
        if discord_manager.client:
            try:
                client_status = discord_manager.client.get_status()
                manager_status.update({"client_details": client_status})
            except Exception as client_e:
                manager_status.update({"client_details": {"error": str(client_e)}})

        return APIResponse.success_response(
            data=manager_status, message="Discord状态获取成功"
        )
    except Exception as e:
        # 返回基本状态而不是抛出异常
        return APIResponse.success_response(
            data={
                "is_running": False,
                "error": str(e),
                "task_done": True,
                "client_status": None,
            },
            message="Discord状态获取失败",
        )


@router.post("/discord/start", response_model=APIResponse[dict])
async def start_discord_listener():
    """手动启动Discord监听器"""
    try:
        if discord_manager.is_running:
            return APIResponse(
                data={"message": "Discord listener is already running"},
                status="warning",
            )

        await discord_manager.start()

        return APIResponse(
            data={"message": "Discord listener started successfully"},
            status="success",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start Discord listener: {str(e)}",
        )


@router.post("/discord/stop", response_model=APIResponse[dict])
async def stop_discord_listener():
    """手动停止Discord监听器"""
    try:
        if not discord_manager.is_running:
            return APIResponse(
                data={"message": "Discord listener is not running"},
                status="warning",
            )

        await discord_manager.stop()

        return APIResponse(
            data={"message": "Discord listener stopped successfully"},
            status="success",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop Discord listener: {str(e)}",
        )


@router.post("/discord/restart", response_model=APIResponse[dict])
async def restart_discord_listener():
    """重启Discord监听器"""
    try:
        # 先停止
        if discord_manager.is_running:
            await discord_manager.stop()

        # 等待一秒确保完全停止
        import asyncio

        await asyncio.sleep(1)

        # 重新启动
        await discord_manager.start()

        return APIResponse(
            data={"message": "Discord listener restarted successfully"},
            status="success",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to restart Discord listener: {str(e)}",
        )


@router.get("/discord/stats", response_model=APIResponse[dict])
async def get_discord_stats():
    """获取Discord处理统计信息"""
    try:
        if not discord_manager.client:
            return APIResponse(
                status="success",
                data={
                    "error": "Discord client not available",
                    "is_running": discord_manager.is_running,
                    "stats": {},
                },
            )

        stats = discord_manager.client.signal_processor.get_processing_stats()

        # 添加额外的统计信息
        enhanced_stats = {
            **stats,
            "monitored_channels": len(discord_manager.client.monitored_channels)
            if hasattr(discord_manager.client, "monitored_channels")
            else 0,
            "reconnect_attempts": getattr(
                discord_manager.client, "reconnect_attempts", 0
            ),
            "max_reconnect_attempts": getattr(
                discord_manager.client, "max_reconnect_attempts", 5
            ),
            "deduplicator_cache_size": len(
                discord_manager.client.deduplicator.message_hashes
            )
            if hasattr(discord_manager.client, "deduplicator")
            and hasattr(discord_manager.client.deduplicator, "message_hashes")
            else 0,
            "connection_status": "connected"
            if not discord_manager.client.is_closed()
            else "disconnected",
        }

        return APIResponse.success_response(
            data=enhanced_stats, message="Discord统计获取成功"
        )
    except Exception as e:
        return APIResponse.success_response(
            data={
                "error": f"Failed to get Discord stats: {str(e)}",
                "stats": {},
            },
            message="Discord统计获取失败",
        )


@router.get("/discord/channels", response_model=APIResponse[list])
async def get_monitored_channels():
    """获取监控频道列表 - 简化版本，从统一配置获取"""
    try:
        if not discord_manager.client:
            raise HTTPException(status_code=404, detail="Discord client not available")

        # 从统一的过滤配置中获取频道信息
        filter_config = discord_manager.client.filter_config
        channels = []

        for channel_id in filter_config.channel_ids:
            channels.append(
                {
                    "channel_id": channel_id,
                    "channel_name": f"Channel {channel_id}",  # 简化版本不存储频道名称
                    "enabled": True,  # 在配置中的频道都是启用的
                    "monitored": True,
                }
            )

        return APIResponse(data=channels)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get monitored channels: {str(e)}",
        )


@router.get("/dashboard/summary", response_model=APIResponse[dict])
async def get_dashboard_summary(db: AsyncSession = Depends(get_db)):
    """获取仪表盘摘要信息"""
    try:
        from sqlalchemy import func, select

        from app.core.auth import get_current_user
        from app.core.models import Order

        # 这里简化实现，实际应该根据当前用户获取数据
        # 由于测试环境的限制，我们返回模拟数据
        # 获取订单统计
        total_orders_query = select(func.count(Order.id))
        total_orders_result = await db.execute(total_orders_query)
        total_orders = total_orders_result.scalar() or 0

        active_orders_query = select(func.count(Order.id)).where(
            Order.status == "active"
        )
        active_orders_result = await db.execute(active_orders_query)
        active_orders = active_orders_result.scalar() or 0

        summary = {
            "total_orders": total_orders,
            "active_orders": active_orders,
            "completed_orders": total_orders - active_orders,
            "total_pnl": 0.0,  # TODO: 计算实际PnL
            "today_pnl": 0.0,  # TODO: 计算今日PnL
            "success_rate": 0.0,  # TODO: 计算成功率
            "active_symbols": [],  # TODO: 获取活跃交易对
            "last_updated": datetime.utcnow().isoformat(),
        }

        return APIResponse.success_response(data=summary, message="仪表板摘要获取成功")
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get dashboard summary: {str(e)}",
        )
