/**
 * 测试置信度等级筛选器功能
 * 在浏览器控制台中运行此脚本来验证新的置信度等级下拉选择框
 */

// 测试置信度等级筛选器的脚本
async function testConfidenceLevelFilter() {
  console.log('=== 开始测试置信度等级筛选器 ===');
  
  try {
    // 等待页面加载完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 1. 查找置信度等级选择框
    console.log('1. 查找置信度等级选择框...');
    const confidenceSelect = document.querySelector('input[aria-label*="置信度等级"]') ||
                             document.querySelector('.v-select input[placeholder*="置信度"]') ||
                             Array.from(document.querySelectorAll('.v-field-label')).find(label => 
                               label.textContent.includes('置信度等级')
                             )?.closest('.v-select');
    
    if (!confidenceSelect) {
      console.log('❌ 没有找到置信度等级选择框');
      return;
    }
    
    console.log('✅ 找到置信度等级选择框');
    
    // 2. 点击选择框打开下拉菜单
    console.log('2. 打开置信度等级下拉菜单...');
    confidenceSelect.click();
    
    // 等待下拉菜单打开
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 检查下拉选项
    console.log('3. 检查下拉选项...');
    const dropdownItems = document.querySelectorAll('.v-list-item');
    const expectedOptions = ['全部', '高 (80%-100%)', '中 (50%-79%)', '低 (0%-49%)'];
    const foundOptions = [];
    
    dropdownItems.forEach(item => {
      const text = item.textContent.trim();
      if (expectedOptions.some(option => text.includes(option.split(' ')[0]))) {
        foundOptions.push(text);
      }
    });
    
    console.log('找到的选项:', foundOptions);
    
    if (foundOptions.length >= 4) {
      console.log('✅ 置信度等级选项正确');
    } else {
      console.log('⚠️ 置信度等级选项不完整');
    }
    
    // 4. 测试选择高置信度
    console.log('4. 测试选择高置信度...');
    const highConfidenceOption = Array.from(dropdownItems).find(item => 
      item.textContent.includes('高') && item.textContent.includes('80%')
    );
    
    if (highConfidenceOption) {
      highConfidenceOption.click();
      console.log('✅ 选择了高置信度选项');
      
      // 等待筛选器更新
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 检查是否触发了筛选
      console.log('5. 检查筛选结果...');
      const signalRows = document.querySelectorAll('.v-data-table tbody tr');
      console.log(`当前显示 ${signalRows.length} 条信号记录`);
      
    } else {
      console.log('❌ 没有找到高置信度选项');
    }
    
    // 6. 测试重置功能
    console.log('6. 测试重置功能...');
    const resetBtn = document.querySelector('[data-testid="clear-filters-btn"]') ||
                    Array.from(document.querySelectorAll('.v-btn')).find(btn => 
                      btn.textContent.includes('重置')
                    );
    
    if (resetBtn) {
      resetBtn.click();
      console.log('✅ 点击了重置按钮');
      
      // 等待重置完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 检查置信度选择框是否重置
      const selectInput = document.querySelector('input[aria-label*="置信度等级"]');
      if (selectInput && (!selectInput.value || selectInput.value === '')) {
        console.log('✅ 置信度等级筛选器已重置');
      } else {
        console.log('⚠️ 置信度等级筛选器重置可能有问题');
      }
    } else {
      console.log('❌ 没有找到重置按钮');
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
  
  console.log('=== 置信度等级筛选器测试完成 ===');
}

// 监控置信度筛选相关的网络请求
function monitorConfidenceFilterRequests() {
  console.log('=== 开始监控置信度筛选相关的网络请求 ===');
  
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('/api/v1/signals') && url.includes('?')) {
      console.log('🌐 信号筛选API请求:', url);
      
      // 检查URL中的置信度参数
      const urlObj = new URL(url, window.location.origin);
      const confidenceMin = urlObj.searchParams.get('confidence_min');
      const confidenceMax = urlObj.searchParams.get('confidence_max');
      
      if (confidenceMin !== null || confidenceMax !== null) {
        console.log('📊 置信度筛选参数:');
        console.log(`  - confidence_min: ${confidenceMin}`);
        console.log(`  - confidence_max: ${confidenceMax}`);
        
        // 判断置信度等级
        if (confidenceMin === '0.8' && confidenceMax === '1') {
          console.log('✅ 检测到高置信度筛选 (80%-100%)');
        } else if (confidenceMin === '0.5' && confidenceMax === '0.79') {
          console.log('✅ 检测到中置信度筛选 (50%-79%)');
        } else if (confidenceMin === '0' && confidenceMax === '0.49') {
          console.log('✅ 检测到低置信度筛选 (0%-49%)');
        } else if (confidenceMin === '0' && confidenceMax === '1') {
          console.log('✅ 检测到全部置信度 (0%-100%)');
        } else {
          console.log('⚠️ 检测到自定义置信度范围');
        }
      }
      
      return originalFetch.apply(this, args).then(response => {
        if (response.ok) {
          const clonedResponse = response.clone();
          clonedResponse.json().then(data => {
            if (data.success && data.data && data.data.items) {
              console.log(`📈 返回 ${data.data.items.length} 条筛选结果`);
            }
          }).catch(() => {
            // 忽略JSON解析错误
          });
        }
        return response;
      });
    }
    return originalFetch.apply(this, args);
  };
  
  console.log('置信度筛选网络请求监控已启用');
}

// 检查页面元素
function checkConfidenceLevelElements() {
  console.log('=== 检查置信度等级相关页面元素 ===');
  
  // 检查是否还有旧的范围滑块
  const rangeSlider = document.querySelector('.v-range-slider');
  if (rangeSlider) {
    console.log('⚠️ 发现旧的范围滑块，可能需要清理');
  } else {
    console.log('✅ 没有发现旧的范围滑块');
  }
  
  // 检查新的选择框
  const selectElements = document.querySelectorAll('.v-select');
  let confidenceSelectFound = false;
  
  selectElements.forEach((select, index) => {
    const label = select.querySelector('.v-field-label');
    if (label && label.textContent.includes('置信度等级')) {
      confidenceSelectFound = true;
      console.log(`✅ 找到置信度等级选择框 (索引: ${index})`);
    }
  });
  
  if (!confidenceSelectFound) {
    console.log('❌ 没有找到置信度等级选择框');
  }
  
  console.log('=== 页面元素检查完成 ===');
}

// 导出函数供控制台使用
window.testConfidenceLevelFilter = testConfidenceLevelFilter;
window.monitorConfidenceFilterRequests = monitorConfidenceFilterRequests;
window.checkConfidenceLevelElements = checkConfidenceLevelElements;

console.log('置信度等级筛选器测试脚本已加载！');
console.log('使用方法:');
console.log('1. checkConfidenceLevelElements() - 检查页面元素');
console.log('2. monitorConfidenceFilterRequests() - 监控网络请求');
console.log('3. testConfidenceLevelFilter() - 自动测试置信度等级筛选器');
