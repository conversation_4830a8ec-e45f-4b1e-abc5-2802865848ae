import { createRouter, createWebHistory } from 'vue-router'
import { SecureStorage } from '@/utils/secureStorage'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue')
    },
    {
      path: '/orders',
      name: 'orders',
      component: () => import('../views/OrdersView.vue')
    },
    {
      path: '/conditional-orders',
      name: 'conditional-orders',
      component: () => import('../views/ConditionalOrdersView.vue')
    },
    {
      path: '/configs',
      name: 'configs',
      component: () => import('../views/ConfigsView.vue')
    },
    {
      path: '/signals',
      name: 'signals',
      component: () => import('../views/SignalsView.vue')
    },
    {
      path: '/signal-card-demo',
      name: 'signal-card-demo',
      component: () => import('../views/SignalCardDemo.vue')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const publicPages = ['/login']
  const authRequired = !publicPages.includes(to.path)

  // 使用SecureStorage进行认证检查
  let loggedIn = false
  try {
    const token = SecureStorage.getToken()
    const user = SecureStorage.getUser()
    loggedIn = !!(token && user)
  } catch (error) {
    console.error('Auth check failed:', error)
    loggedIn = false
  }

  if (authRequired && !loggedIn) {
    return next('/login')
  }

  next()
})

export default router