<template>
  <v-container fluid data-testid="signals-view">
    <v-row>
      <v-col cols="12">
        <!-- 错误状态显示 -->
        <v-alert
          v-if="error"
          type="error"
          variant="tonal"
          class="error-message mb-4"
          closable
          @click:close="error = null"
        >
          <v-alert-title>
            <v-icon left>mdi-alert-circle</v-icon>
            操作失败
          </v-alert-title>
          {{ error }}
        </v-alert>

        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon left color="primary">mdi-signal</v-icon>
            <span class="text-h5">信号管理</span>
            <v-spacer></v-spacer>

            <!-- 统计信息 -->
            <div class="d-flex align-center mr-4">
              <v-chip
                v-if="stats"
                color="primary"
                variant="outlined"
                size="small"
                class="mr-2"
              >
                <v-icon left size="small">mdi-chart-line</v-icon>
                总计: {{ stats.total_signals }}
              </v-chip>
              <v-chip
                v-if="stats"
                color="success"
                variant="outlined"
                size="small"
                class="mr-2"
              >
                <v-icon left size="small">mdi-check-circle</v-icon>
                已处理: {{ stats.processed_signals }}
              </v-chip>
            </div>

            <!-- 操作按钮 -->
            <v-btn
              color="primary"
              @click="openCreateDialog"
              :loading="loading"
              data-testid="create-signal-btn"
            >
              <v-icon left>mdi-plus</v-icon>
              手动添加信号
            </v-btn>
          </v-card-title>

          <!-- 筛选器 -->
          <SignalFilters
            :model-value="filters"
            @update:model-value="handleFiltersUpdate"
            @filter-change="handleFilterChange"
            :loading="loading"
          />

          <!-- 信号列表 -->
          <SignalsList
            :signals="signals"
            :loading="loading"
            :pagination="pagination"
            @signal-click="showSignalDetails"
            @signal-update="handleSignalUpdate"
            @page-change="handlePageChange"
            @sort-change="handleSortChange"
          />
        </v-card>
      </v-col>
    </v-row>

    <!-- 详情对话框 -->
    <SignalDetailsDialog
      v-model="showDetails"
      :signal="selectedSignal"
      @update="handleSignalUpdate"
      @close="selectedSignal = null"
    />

    <!-- 创建信号对话框 -->
    <CreateSignalDialog
      v-model="showCreateDialog"
      @created="handleSignalCreated"
    />

    <!-- 全局加载状态 -->
    <v-overlay
      v-model="globalLoading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
    </v-overlay>
  </v-container>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useSnackbar } from '@/composables/useSnackbar'
import { signalApi } from '@/api/signals'
import SignalFilters from '@/components/signals/SignalFilters.vue'
import SignalsList from '@/components/signals/SignalsList.vue'
import SignalDetailsDialog from '@/components/signals/SignalDetailsDialog.vue'
import CreateSignalDialog from '@/components/signals/CreateSignalDialog.vue'

// 组合式API
const router = useRouter()
const { showSnackbar } = useSnackbar()

// 响应式数据
const loading = ref(false)
const globalLoading = ref(false)
const signals = ref([])
const stats = ref(null)
const selectedSignal = ref(null)
const showDetails = ref(false)
const showCreateDialog = ref(false)
const error = ref(null)

// 筛选器状态
const filters = reactive({
  platform: '',
  status: '',
  channel_id: '',
  ai_parsed: '',
  ai_message_type: '',
  llm_service: '',
  confidence_range: [0, 100],
  date_range: null,
  search: ''
})

// 分页状态
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  has_next: false,
  has_prev: false
})

// 排序状态
const sorting = reactive({
  sort_by: 'created_at',
  sort_order: 'desc'
})

// 计算属性
const dateRangeText = computed(() => {
  if (!filters.date_range || filters.date_range.length !== 2) {
    return ''
  }
  return `${filters.date_range[0]} ~ ${filters.date_range[1]}`
})

// 方法
const loadSignals = async () => {
  try {
    loading.value = true
    error.value = null

    // 基础参数
    const params = {
      page: pagination.page,
      size: pagination.size,
      sort_by: sorting.sort_by,
      sort_order: sorting.sort_order
    }

    // 映射前端筛选器字段到后端API字段
    const filterMapping = {
      platform: 'platform',
      status: 'is_processed', // 修复：status -> is_processed
      channel_id: 'channel_id',
      ai_parsed: 'ai_parse_status', // 修复：ai_parsed -> ai_parse_status
      ai_message_type: 'message_type_ai', // 修复：ai_message_type -> message_type_ai
      llm_service: 'llm_service',
      search: 'search'
    }

    // 处理普通筛选器参数
    Object.keys(filterMapping).forEach(frontendKey => {
      const backendKey = filterMapping[frontendKey]
      const value = filters[frontendKey]

      if (value !== null && value !== undefined && value !== '') {
        // 特殊处理status字段的值映射
        if (frontendKey === 'status') {
          if (value === 'unprocessed') {
            params[backendKey] = false
          } else if (value === 'processed') {
            params[backendKey] = true
          } else if (typeof value === 'boolean') {
            params[backendKey] = value
          }
        } else {
          params[backendKey] = value
        }
      }
    })

    // 处理置信度范围 - 修复：转换为min/max参数
    if (filters.confidence_range && Array.isArray(filters.confidence_range)) {
      const [min, max] = filters.confidence_range
      // 只有当范围不是默认的[0,100]时才添加
      if (min !== 0 || max !== 100) {
        params.confidence_min = min / 100 // 转换为0-1范围
        params.confidence_max = max / 100 // 转换为0-1范围
      }
    }

    // 处理日期范围
    if (filters.date_range && filters.date_range.length === 2) {
      params.date_from = filters.date_range[0] + 'T00:00:00'
      params.date_to = filters.date_range[1] + 'T23:59:59'
    }

    console.log('🔍 loadSignals参数:', JSON.stringify(params))

    const response = await signalApi.getSignals(params)

    if (response.success) {
      signals.value = response.data.items
      pagination.total = response.data.total
      pagination.has_next = response.data.has_next
      pagination.has_prev = response.data.has_prev
    } else {
      error.value = response.message || '获取信号列表失败'
      showSnackbar(response.message || '获取信号列表失败', 'error')
    }
  } catch (err) {
    console.error('加载信号列表失败:', err)
    error.value = '加载信号列表失败，请稍后重试'
    showSnackbar('加载信号列表失败', 'error')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await signalApi.getStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleFiltersUpdate = (newFilters) => {
  console.log('🔍 [SignalsView] 接收到筛选器更新:', JSON.stringify(newFilters))
  // 更新本地筛选器状态
  Object.assign(filters, newFilters)
}

const handleFilterChange = () => {
  console.log('🔍 [SignalsView] 筛选器变化:', JSON.stringify(filters))
  pagination.page = 1 // 重置到第一页
  loadSignals()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadSignals()
}

const handleSortChange = (sortBy, sortOrder) => {
  sorting.sort_by = sortBy
  sorting.sort_order = sortOrder
  pagination.page = 1 // 重置到第一页
  loadSignals()
}

const showSignalDetails = async (signal) => {
  try {
    error.value = null

    // 获取信号ID
    const signalId = typeof signal === 'string' || typeof signal === 'number'
      ? signal
      : signal.id

    // 总是调用详情API获取完整信息（包括技术字段）
    const response = await signalApi.getSignalDetail(signalId)
    if (response.success) {
      selectedSignal.value = response.data
    } else {
      error.value = response.message || '信号不存在或已被删除'
      showSnackbar(error.value, 'error')
      return
    }

    showDetails.value = true
  } catch (err) {
    console.error('获取信号详情失败:', err)
    error.value = '获取信号详情失败，请稍后重试'
    showSnackbar(error.value, 'error')
  }
}

const handleSignalUpdate = async (updatedSignal) => {
  // 检查updatedSignal是否有效
  if (!updatedSignal || !updatedSignal.id) {
    console.warn('收到无效的信号更新数据:', updatedSignal)
    showSnackbar('信号更新失败：数据无效', 'error')
    return
  }

  // 更新本地列表中的信号
  const index = signals.value.findIndex(s => s && s.id === updatedSignal.id)
  if (index !== -1) {
    signals.value[index] = updatedSignal
  }

  // 重新加载统计信息
  await loadStats()

  showSnackbar('信号更新成功', 'success')
}

const openCreateDialog = () => {
  showCreateDialog.value = true
}

const handleSignalCreated = async (newSignal) => {
  showCreateDialog.value = false

  // 重新加载数据
  await Promise.all([loadSignals(), loadStats()])

  showSnackbar('信号创建成功', 'success')
}

// 生命周期
onMounted(async () => {
  globalLoading.value = true
  try {
    await Promise.all([loadSignals(), loadStats()])
  } finally {
    globalLoading.value = false
  }
})

// 监听筛选器变化 - 注释掉，避免与@filter-change事件重复
// watch(
//   () => ({ ...filters }),
//   () => {
//     handleFilterChange()
//   },
//   { deep: true }
// )
</script>

<style scoped>
.v-card-title {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.v-chip {
  font-weight: 500;
}
</style>