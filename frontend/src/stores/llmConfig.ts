/**
 * LLM配置状态管理
 * 使用Pinia管理LLM配置的状态和操作
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  LLMConfig, 
  LLMConfigRequest, 
  LLMConfigTestRequest, 
  LLMConfigTestResponse,
  LLMProvider 
} from '@/types/llm.types'
import * as llmConfigApi from '@/api/llmConfig'
import { useSnackbar } from '@/composables/useSnackbar'

export const useLLMConfigStore = defineStore('llmConfig', () => {
  // 状态
  const configs = ref<LLMConfig[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const testResults = ref<Record<string, LLMConfigTestResponse>>({})

  // 计算属性
  const enabledConfigs = computed(() => 
    configs.value.filter(config => config.enabled)
  )

  const defaultConfig = computed(() => 
    configs.value.find(config => config.is_default) || null
  )

  const configsByProvider = computed(() => {
    return configs.value.reduce((groups, config) => {
      const provider = config.provider
      if (!groups[provider]) {
        groups[provider] = []
      }
      groups[provider].push(config)
      return groups
    }, {} as Record<string, LLMConfig[]>)
  })

  const configStats = computed(() => ({
    total: configs.value.length,
    enabled: enabledConfigs.value.length,
    byProvider: Object.entries(configsByProvider.value).reduce(
      (stats, [provider, providerConfigs]) => {
        stats[provider] = providerConfigs.length
        return stats
      },
      {} as Record<string, number>
    )
  }))

  // 操作方法
  const { showSnackbar } = useSnackbar()

  /**
   * 获取所有LLM配置
   */
  async function fetchConfigs() {
    loading.value = true
    error.value = null
    
    try {
      const response = await llmConfigApi.getLLMConfigs()
      configs.value = response
    } catch (err: any) {
      error.value = err.message || '获取LLM配置失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建LLM配置
   */
  async function createConfig(configData: LLMConfigRequest): Promise<LLMConfig> {
    loading.value = true
    error.value = null

    try {
      const newConfig = await llmConfigApi.createLLMConfig(configData)
      configs.value.push(newConfig)
      showSnackbar('LLM配置创建成功', 'success')
      return newConfig
    } catch (err: any) {
      error.value = err.message || '创建LLM配置失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新LLM配置
   */
  async function updateConfig(id: string, configData: LLMConfigRequest): Promise<LLMConfig> {
    loading.value = true
    error.value = null

    try {
      const updatedConfig = await llmConfigApi.updateLLMConfig(id, configData)
      const index = configs.value.findIndex(config => config.id === id)
      if (index !== -1) {
        configs.value[index] = updatedConfig
      }
      showSnackbar('LLM配置更新成功', 'success')
      return updatedConfig
    } catch (err: any) {
      error.value = err.message || '更新LLM配置失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除LLM配置
   */
  async function deleteConfig(id: string): Promise<void> {
    loading.value = true
    error.value = null

    try {
      await llmConfigApi.deleteLLMConfig(id)
      configs.value = configs.value.filter(config => config.id !== id)
      // 清除测试结果
      delete testResults.value[id]
      showSnackbar('LLM配置删除成功', 'success')
    } catch (err: any) {
      error.value = err.message || '删除LLM配置失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置默认配置
   */
  async function setDefaultConfig(id: string): Promise<void> {
    loading.value = true
    error.value = null

    try {
      await llmConfigApi.setDefaultLLMConfig(id)
      // 更新本地状态
      configs.value.forEach(config => {
        config.is_default = config.id === id
      })
      showSnackbar('默认LLM配置设置成功', 'success')
    } catch (err: any) {
      error.value = err.message || '设置默认LLM配置失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 测试LLM配置
   */
  async function testConfig(id: string, testData?: LLMConfigTestRequest): Promise<LLMConfigTestResponse> {
    loading.value = true
    error.value = null

    try {
      const result = await llmConfigApi.testLLMConfig(id, testData)
      testResults.value[id] = result
      
      if (result.success) {
        showSnackbar('LLM配置测试成功', 'success')
      } else {
        showSnackbar(`LLM配置测试失败: ${result.error_message}`, 'warning')
      }
      
      return result
    } catch (err: any) {
      error.value = err.message || '测试LLM配置失败'
      const failedResult: LLMConfigTestResponse = {
        success: false,
        error_message: error.value
      }
      testResults.value[id] = failedResult
      showSnackbar(error.value, 'error')
      return failedResult
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换配置启用状态
   */
  async function toggleConfig(id: string): Promise<void> {
    const config = configs.value.find(c => c.id === id)
    if (!config) return

    const newEnabled = !config.enabled

    try {
      const updateData: any = {
        config_name: config.config_name,
        provider: config.provider as LLMProvider,
        enabled: newEnabled,
        is_default: config.is_default,
        api_key: null, // 明确设置为null
        api_base_url: config.api_base_url,
        model_name: config.model_name,
        max_tokens: config.max_tokens,
        temperature: config.temperature,
        timeout_seconds: config.timeout_seconds,
        max_retries: config.max_retries
      }
      await updateConfig(id, updateData)
    } catch (err) {
      // 错误已在updateConfig中处理
      throw err
    }
  }

  /**
   * 复制配置
   */
  async function duplicateConfig(id: string, newName: string): Promise<LLMConfig> {
    loading.value = true
    error.value = null

    try {
      const duplicatedConfig = await llmConfigApi.duplicateLLMConfig(id, newName)
      configs.value.push(duplicatedConfig)
      showSnackbar('LLM配置复制成功', 'success')
      return duplicatedConfig
    } catch (err: any) {
      error.value = err.message || '复制LLM配置失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量操作：启用/禁用多个配置
   */
  async function batchToggleConfigs(ids: string[], enabled: boolean): Promise<void> {
    loading.value = true
    error.value = null

    try {
      await llmConfigApi.batchToggleLLMConfigs(ids, enabled)
      // 更新本地状态
      configs.value.forEach(config => {
        if (ids.includes(config.id)) {
          config.enabled = enabled
        }
      })
      const action = enabled ? '启用' : '禁用'
      showSnackbar(`批量${action}LLM配置成功`, 'success')
    } catch (err: any) {
      error.value = err.message || '批量操作失败'
      showSnackbar(error.value, 'error')
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取配置的测试结果
   */
  function getTestResult(id: string): LLMConfigTestResponse | null {
    return testResults.value[id] || null
  }

  /**
   * 清除测试结果
   */
  function clearTestResult(id: string): void {
    delete testResults.value[id]
  }

  /**
   * 清除所有测试结果
   */
  function clearAllTestResults(): void {
    testResults.value = {}
  }

  /**
   * 根据ID获取配置
   */
  function getConfigById(id: string): LLMConfig | null {
    return configs.value.find(config => config.id === id) || null
  }

  /**
   * 根据提供商获取配置
   */
  function getConfigsByProvider(provider: string): LLMConfig[] {
    return configs.value.filter(config => config.provider === provider)
  }

  /**
   * 验证配置名称是否唯一
   */
  async function isConfigNameUnique(name: string, excludeId?: string): Promise<boolean> {
    return llmConfigApi.isLLMConfigNameUnique(name, excludeId)
  }

  /**
   * 重置状态
   */
  function resetState(): void {
    configs.value = []
    loading.value = false
    error.value = null
    testResults.value = {}
  }

  return {
    // 状态
    configs,
    loading,
    error,
    testResults,
    
    // 计算属性
    enabledConfigs,
    defaultConfig,
    configsByProvider,
    configStats,
    
    // 操作方法
    fetchConfigs,
    createConfig,
    updateConfig,
    deleteConfig,
    setDefaultConfig,
    testConfig,
    toggleConfig,
    duplicateConfig,
    batchToggleConfigs,
    getTestResult,
    clearTestResult,
    clearAllTestResults,
    getConfigById,
    getConfigsByProvider,
    isConfigNameUnique,
    resetState
  }
})
