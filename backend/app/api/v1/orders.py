from typing import List, Optional
import time
import uuid
from datetime import datetime, timezone

import structlog
from fastapi import APIRouter, Body, Depends, HTTPException, status, Query
from sqlalchemy import and_, desc, func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.auth import get_current_user
from ...core.database import get_db
from ...core.models import Order, User
from ...core.schemas import (
    OrderListResponse,
    OrderResponse,
    OrderCreate,
    APIResponse,
)

# 配置结构化日志
logger = structlog.get_logger()

router = APIRouter(prefix="/orders", tags=["orders"])


@router.get("", response_model=APIResponse[OrderListResponse])
async def get_orders(
    current_user: User = Depends(get_current_user),
    symbol: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = Query(default=100, ge=1, le=1000, description="返回记录数量限制"),
    offset: int = Query(default=0, ge=0, description="分页偏移量"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取订单列表

    Args:
        current_user: 当前用户
        symbol: 交易对，可选
        status: 订单状态，可选
        limit: 返回记录数量限制
        offset: 分页偏移量
        db: 数据库会话

    Returns:
        订单列表
    """
    # 构建查询
    query = select(Order).where(Order.user_id == current_user.id)

    # 添加过滤条件
    if symbol:
        query = query.where(Order.symbol == symbol)
    if status:
        query = query.where(Order.status == status)

    # 添加排序和分页
    query = query.order_by(Order.created_at.desc())
    query = query.limit(limit).offset(offset)

    # 执行查询
    result = await db.execute(query)
    orders = result.scalars().all()

    # 获取总数
    count_query = select(Order).where(Order.user_id == current_user.id)
    if symbol:
        count_query = count_query.where(Order.symbol == symbol)
    if status:
        count_query = count_query.where(Order.status == status)

    count_result = await db.execute(count_query)
    total_count = len(count_result.scalars().all())

    order_responses = [OrderResponse.model_validate(order) for order in orders]

    # Return the data structure that tests expect
    return APIResponse.success_response(
        data={
            "orders": order_responses,
            "total": total_count,
            "limit": limit,
            "offset": offset,
        },
        message="订单列表获取成功",
    )


@router.get("/{order_id}", response_model=APIResponse[OrderResponse])
async def get_order(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取单个订单详情

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        订单详情
    """
    # 验证订单ID格式
    try:
        # 尝试将字符串转换为UUID以验证格式
        uuid_obj = uuid.UUID(order_id)
        validated_order_id = str(uuid_obj)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid order ID format: {order_id}. Expected UUID format.",
        )

    query = select(Order).where(
        Order.id == validated_order_id, Order.user_id == current_user.id
    )
    result = await db.execute(query)
    order = result.scalar_one_or_none()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Order with ID {order_id} not found",
        )

    order_response = OrderResponse.model_validate(order)
    return APIResponse.success_response(data=order_response, message="订单详情获取成功")


@router.post("", response_model=APIResponse[OrderResponse])
async def create_order(
    order_data: OrderCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建新订单

    Args:
        order_data: 订单创建数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        创建的订单信息
    """
    # 使用数据库事务确保数据一致性
    try:
        # 检查用户是否有权限创建订单
        from ...services.dynamic_risk_manager import DynamicRiskManager

        risk_manager = DynamicRiskManager(db)

        # 获取用户风控配置
        risk_config = await risk_manager.get_user_risk_config(current_user.id)

        # 检查并发订单数量限制
        active_orders_count = select(func.count(Order.id)).where(
            and_(Order.user_id == current_user.id, Order.status == "active")
        )
        active_count = await db.scalar(active_orders_count)

        if active_count >= risk_config.get("max_open_positions", 5):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"超过最大并发订单数量限制 ({risk_config.get('max_open_positions', 5)})",
            )

        # 生成客户端订单ID（确保唯一性）
        client_order_id = f"order_{uuid.uuid4().hex[:12]}"

        # 检查客户端订单ID是否已存在（防止重复提交）
        existing_order = await db.execute(
            select(Order).where(Order.client_order_id == client_order_id)
        )
        if existing_order.scalar_one_or_none():
            # 重新生成ID
            client_order_id = f"order_{uuid.uuid4().hex[:12]}_{int(time.time())}"

        # 验证订单类型和价格
        order_type = getattr(order_data, "order_type", "MARKET").upper()
        price = getattr(order_data, "price", None)

        # 验证订单类型
        valid_order_types = ["MARKET", "LIMIT", "STOP", "STOP_LIMIT"]
        if order_type not in valid_order_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid order type. Must be one of: {', '.join(valid_order_types)}",
            )

        # 限价单和止损限价单必须提供价格
        if order_type in ["LIMIT", "STOP_LIMIT"] and price is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{order_type} orders require a price",
            )

        # 市价单不应该有价格
        if order_type == "MARKET" and price is not None:
            logger.warning(f"Market order with price {price} provided, ignoring price")
            price = None

        # 创建订单对象
        new_order = Order(
            user_id=current_user.id,
            client_order_id=client_order_id,
            symbol=order_data.symbol,
            side=order_data.side.lower(),
            quantity=order_data.quantity,
            entry_price=price,
            status="active",
            agent_log={
                "created_by": "api",
                "order_type": order_type,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "risk_check_passed": True,
                "active_orders_count": active_count,
                "validation_passed": True,
            },
        )

        db.add(new_order)
        await db.flush()  # 获取ID但不提交

        # 记录订单创建日志
        logger.info(
            "Order created successfully",
            user_id=current_user.id,
            order_id=str(new_order.id),
            client_order_id=client_order_id,
            symbol=order_data.symbol,
            side=order_data.side,
            quantity=str(order_data.quantity),
        )

        # 手动提交事务
        await db.commit()
        await db.refresh(new_order)

        order_response = OrderResponse.model_validate(new_order)
        return APIResponse.success_response(data=order_response, message="订单创建成功")

    except HTTPException:
        # 回滚事务并重新抛出HTTP异常
        await db.rollback()
        raise
    except IntegrityError as e:
        # 回滚事务并处理数据库完整性错误
        await db.rollback()
        logger.error("Database integrity error during order creation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="订单创建失败：数据冲突，可能是重复的订单ID",
        )
    except Exception as e:
        # 回滚事务并处理其他未预期的错误
        await db.rollback()
        logger.error("Unexpected error during order creation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"订单创建失败：{str(e)}",
        )


@router.post("/{order_id}/close", response_model=APIResponse[OrderResponse])
async def close_order(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    关闭订单

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        关闭后的订单信息
    """
    # 验证订单ID格式
    try:
        uuid_obj = uuid.UUID(order_id)
        validated_order_id = str(uuid_obj)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid order ID format: {order_id}. Expected UUID format.",
        )

    # 查询订单
    query = select(Order).where(
        Order.id == validated_order_id, Order.user_id == current_user.id
    )
    result = await db.execute(query)
    order = result.scalar_one_or_none()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Order with ID {order_id} not found",
        )

    # 检查订单状态
    if order.status in ["closed", "cancelled", "filled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Order is already {order.status} and cannot be closed",
        )

    # 更新订单状态
    order.status = "closed"
    order.closed_at = datetime.now()

    await db.commit()
    await db.refresh(order)

    order_response = OrderResponse.model_validate(order)
    return APIResponse.success_response(data=order_response, message="订单关闭成功")


@router.patch("/{order_id}", response_model=APIResponse[OrderResponse])
async def update_order(
    order_id: str,
    status_update: str = Body(..., description="新的订单状态"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新订单状态

    Args:
        order_id: 订单ID
        status_update: 新的订单状态
        current_user: 当前用户
        db: 数据库会话

    Returns:
        更新后的订单信息
    """
    # 验证订单ID格式
    try:
        uuid_obj = uuid.UUID(order_id)
        validated_order_id = str(uuid_obj)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid order ID format: {order_id}. Expected UUID format.",
        )

    # 查询订单
    query = select(Order).where(
        Order.id == validated_order_id, Order.user_id == current_user.id
    )
    result = await db.execute(query)
    order = result.scalar_one_or_none()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Order with ID {order_id} not found",
        )

    # 验证状态值
    valid_statuses = [
        "active",
        "closed",
        "cancelled",
        "filled",
        "partially_filled",
    ]
    if status_update.lower() not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}",
        )

    # 更新订单状态
    order.status = status_update.lower()
    if status_update.lower() in ["closed", "cancelled"]:
        order.closed_at = datetime.now()

    await db.commit()
    await db.refresh(order)

    order_response = OrderResponse.model_validate(order)
    return APIResponse.success_response(data=order_response, message="订单状态更新成功")


@router.delete("/{order_id}", response_model=APIResponse[dict])
async def delete_order(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除订单

    Args:
        order_id: 订单ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        删除确认信息
    """
    # 验证订单ID格式
    try:
        uuid_obj = uuid.UUID(order_id)
        validated_order_id = str(uuid_obj)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid order ID format: {order_id}. Expected UUID format.",
        )

    # 查询订单
    query = select(Order).where(
        Order.id == validated_order_id, Order.user_id == current_user.id
    )
    result = await db.execute(query)
    order = result.scalar_one_or_none()

    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Order with ID {order_id} not found",
        )

    # 检查订单是否可以删除
    if order.status in ["filled", "partially_filled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete filled or partially filled orders",
        )

    # 删除订单
    await db.delete(order)
    await db.commit()

    return APIResponse.success_response(
        data={"deleted_order_id": order_id}, message="订单删除成功"
    )


@router.get("/types", response_model=APIResponse[dict])
async def get_order_types():
    """
    获取支持的订单类型和说明

    Returns:
        支持的订单类型列表
    """
    order_types = {
        "MARKET": {
            "name": "市价单",
            "description": "以当前市场价格立即执行的订单",
            "requires_price": False,
            "example": {
                "symbol": "BTC/USDT",
                "side": "buy",
                "quantity": 0.01,
                "order_type": "MARKET",
            },
        },
        "LIMIT": {
            "name": "限价单",
            "description": "以指定价格或更好价格执行的订单",
            "requires_price": True,
            "example": {
                "symbol": "BTC/USDT",
                "side": "buy",
                "quantity": 0.01,
                "price": 50000.0,
                "order_type": "LIMIT",
            },
        },
        "STOP": {
            "name": "止损单",
            "description": "当价格达到止损价时转为市价单",
            "requires_price": True,
            "example": {
                "symbol": "BTC/USDT",
                "side": "sell",
                "quantity": 0.01,
                "price": 48000.0,
                "order_type": "STOP",
            },
        },
        "STOP_LIMIT": {
            "name": "止损限价单",
            "description": "当价格达到止损价时转为限价单",
            "requires_price": True,
            "example": {
                "symbol": "BTC/USDT",
                "side": "sell",
                "quantity": 0.01,
                "price": 48000.0,
                "order_type": "STOP_LIMIT",
            },
        },
    }

    return APIResponse.success_response(data=order_types, message="订单类型获取成功")
