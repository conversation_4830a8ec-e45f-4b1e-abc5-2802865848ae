/**
 * 测试信号筛选器布局优化
 * 在浏览器控制台中运行此脚本来验证筛选器的布局和顺序
 */

// 测试筛选器布局的脚本
function testFilterLayout() {
  console.log('=== 开始测试筛选器布局优化 ===');
  
  try {
    // 1. 检查筛选器的顺序
    console.log('1. 检查筛选器顺序...');
    
    // 第一行筛选器顺序：平台 → 状态 → 频道ID → AI解析状态 → AI消息类型 → LLM服务
    const firstRowFilters = [
      { label: '平台', selector: 'input[aria-label*="平台"]' },
      { label: '处理状态', selector: 'input[aria-label*="处理状态"]' },
      { label: '频道ID', selector: 'input[placeholder*="频道ID"]' },
      { label: 'AI解析状态', selector: 'input[aria-label*="AI解析状态"]' },
      { label: '消息类型', selector: 'input[aria-label*="消息类型"]' },
      { label: 'LLM服务', selector: 'input[aria-label*="LLM服务"]' }
    ];
    
    // 第二行筛选器顺序：置信度等级 → 日期范围 → 搜索
    const secondRowFilters = [
      { label: '置信度等级', selector: 'input[aria-label*="置信度等级"]' },
      { label: '日期范围', selector: 'input[aria-label*="日期范围"]' },
      { label: '搜索信号内容', selector: 'input[aria-label*="搜索信号内容"]' }
    ];
    
    console.log('检查第一行筛选器顺序...');
    firstRowFilters.forEach((filter, index) => {
      const element = document.querySelector(filter.selector);
      if (element) {
        console.log(`✅ ${index + 1}. ${filter.label} - 找到`);
      } else {
        console.log(`❌ ${index + 1}. ${filter.label} - 未找到`);
      }
    });
    
    console.log('检查第二行筛选器顺序...');
    secondRowFilters.forEach((filter, index) => {
      const element = document.querySelector(filter.selector);
      if (element) {
        console.log(`✅ ${index + 1}. ${filter.label} - 找到`);
      } else {
        console.log(`❌ ${index + 1}. ${filter.label} - 未找到`);
      }
    });
    
    // 2. 检查筛选器尺寸一致性
    console.log('2. 检查筛选器尺寸一致性...');
    
    const allSelects = document.querySelectorAll('.v-select');
    const heights = [];
    const densities = [];
    
    allSelects.forEach((select, index) => {
      const input = select.querySelector('.v-field__input');
      if (input) {
        const height = window.getComputedStyle(input).height;
        heights.push(height);
        
        // 检查是否有dense类
        const isDense = select.classList.contains('v-input--density-compact') || 
                       select.classList.contains('v-input--density-default') ||
                       select.querySelector('.v-field--variant-outlined');
        densities.push(isDense);
        
        console.log(`筛选器 ${index + 1}: 高度=${height}, 样式一致=${isDense}`);
      }
    });
    
    // 检查高度是否一致
    const uniqueHeights = [...new Set(heights)];
    if (uniqueHeights.length === 1) {
      console.log('✅ 所有筛选器高度一致');
    } else {
      console.log('⚠️ 筛选器高度不一致:', uniqueHeights);
    }
    
    // 3. 检查响应式布局
    console.log('3. 检查响应式布局...');
    
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    
    console.log(`当前视口尺寸: ${viewport.width}x${viewport.height}`);
    
    // 检查在不同屏幕尺寸下的列宽度
    const cols = document.querySelectorAll('.v-col');
    const colClasses = [];
    
    cols.forEach((col, index) => {
      const classes = Array.from(col.classList).filter(cls => 
        cls.startsWith('v-col-') || cls.includes('md-') || cls.includes('sm-') || cls.includes('lg-')
      );
      colClasses.push(classes);
      console.log(`列 ${index + 1}: ${classes.join(', ')}`);
    });
    
    // 4. 检查搜索框位置
    console.log('4. 检查搜索框位置...');
    
    const searchInput = document.querySelector('[data-testid="search-input"]');
    if (searchInput) {
      const searchCol = searchInput.closest('.v-col');
      const allCols = Array.from(document.querySelectorAll('.v-col'));
      const searchIndex = allCols.indexOf(searchCol);
      
      console.log(`搜索框位于第 ${searchIndex + 1} 个列位置`);
      
      // 检查搜索框是否在最后一行的最后位置
      const rows = document.querySelectorAll('.v-row');
      if (rows.length >= 2) {
        const secondRow = rows[1];
        const secondRowCols = secondRow.querySelectorAll('.v-col');
        const lastCol = secondRowCols[secondRowCols.length - 1];
        
        if (lastCol.contains(searchInput)) {
          console.log('✅ 搜索框位于第二行的最后位置');
        } else {
          console.log('⚠️ 搜索框位置可能不正确');
        }
      }
    } else {
      console.log('❌ 未找到搜索框');
    }
    
    // 5. 检查筛选器功能
    console.log('5. 测试筛选器功能...');
    
    // 测试置信度等级筛选器
    const confidenceSelect = document.querySelector('input[aria-label*="置信度等级"]');
    if (confidenceSelect) {
      console.log('✅ 置信度等级筛选器可访问');
      
      // 模拟点击打开下拉菜单
      confidenceSelect.click();
      
      setTimeout(() => {
        const dropdownItems = document.querySelectorAll('.v-list-item');
        console.log(`置信度等级选项数量: ${dropdownItems.length}`);
        
        // 关闭下拉菜单
        if (dropdownItems.length > 0) {
          document.body.click();
        }
      }, 500);
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
  
  console.log('=== 筛选器布局测试完成 ===');
}

// 检查筛选器的CSS样式一致性
function checkFilterStyles() {
  console.log('=== 检查筛选器CSS样式一致性 ===');
  
  const selects = document.querySelectorAll('.v-select');
  const textFields = document.querySelectorAll('.v-text-field');
  
  console.log(`找到 ${selects.length} 个选择框和 ${textFields.length} 个文本框`);
  
  // 检查选择框样式
  selects.forEach((select, index) => {
    const field = select.querySelector('.v-field');
    if (field) {
      const styles = window.getComputedStyle(field);
      console.log(`选择框 ${index + 1}:`);
      console.log(`  - 边框: ${styles.border}`);
      console.log(`  - 高度: ${styles.height}`);
      console.log(`  - 内边距: ${styles.padding}`);
      console.log(`  - 变体: ${field.classList.contains('v-field--variant-outlined') ? 'outlined' : 'other'}`);
    }
  });
  
  // 检查文本框样式
  textFields.forEach((textField, index) => {
    const field = textField.querySelector('.v-field');
    if (field) {
      const styles = window.getComputedStyle(field);
      console.log(`文本框 ${index + 1}:`);
      console.log(`  - 边框: ${styles.border}`);
      console.log(`  - 高度: ${styles.height}`);
      console.log(`  - 内边距: ${styles.padding}`);
      console.log(`  - 变体: ${field.classList.contains('v-field--variant-outlined') ? 'outlined' : 'other'}`);
    }
  });
  
  console.log('=== CSS样式检查完成 ===');
}

// 测试不同屏幕尺寸下的布局
function testResponsiveLayout() {
  console.log('=== 测试响应式布局 ===');
  
  const testSizes = [
    { width: 1920, height: 1080, name: '桌面大屏' },
    { width: 1366, height: 768, name: '桌面标准' },
    { width: 768, height: 1024, name: '平板' },
    { width: 375, height: 667, name: '手机' }
  ];
  
  const originalSize = {
    width: window.innerWidth,
    height: window.innerHeight
  };
  
  console.log(`原始尺寸: ${originalSize.width}x${originalSize.height}`);
  
  testSizes.forEach(size => {
    console.log(`\n测试 ${size.name} (${size.width}x${size.height}):`);
    
    // 注意：实际改变窗口大小需要用户操作，这里只是模拟检查
    const isMobile = size.width < 768;
    const isTablet = size.width >= 768 && size.width < 1024;
    const isDesktop = size.width >= 1024;
    
    console.log(`  - 设备类型: ${isMobile ? '手机' : isTablet ? '平板' : '桌面'}`);
    console.log(`  - 预期列布局: ${isMobile ? '单列' : isTablet ? '2-3列' : '多列'}`);
  });
  
  console.log('=== 响应式布局测试完成 ===');
}

// 导出函数供控制台使用
window.testFilterLayout = testFilterLayout;
window.checkFilterStyles = checkFilterStyles;
window.testResponsiveLayout = testResponsiveLayout;

console.log('筛选器布局测试脚本已加载！');
console.log('使用方法:');
console.log('1. testFilterLayout() - 测试筛选器布局和顺序');
console.log('2. checkFilterStyles() - 检查CSS样式一致性');
console.log('3. testResponsiveLayout() - 测试响应式布局');
