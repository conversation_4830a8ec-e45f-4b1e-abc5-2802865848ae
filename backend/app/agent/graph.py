import uuid
from typing import Any, Dict, Optional

import structlog
from langgraph.graph import StateGraph
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.schemas import AgentState, AgentStateDict
from .checkpoint import CheckpointManager
from .error_handler import with_smart_error_handling
from .nodes import (
    analyze_error,
    assess_risk,
    execute_plan,
    generate_plan,
    get_context,
    parse_intents,
    preprocess_text,
    request_user_confirmation,
    route_after_error_analysis,
    route_after_execution,
    route_after_parsing,
    route_after_risk_assessment,
    route_after_user_confirmation,
)

# 配置结构化日志
logger = structlog.get_logger()


def build_agent_graph(db: AsyncSession) -> StateGraph:
    """
    构建交易Agent的状态图

    Args:
        db: 数据库会话，将被注入到需要数据库交互的节点中

    Returns:
        StateGraph: 构建好的状态图
    """
    logger.info("Building Agent graph")

    # 创建状态图，使用TypedDict
    graph = StateGraph(AgentStateDict)

    # 创建包装函数，用于处理异步函数和智能错误恢复
    @with_smart_error_handling(
        "Preprocess", timeout_seconds=30, enable_smart_recovery=True
    )
    async def wrapped_preprocess(state: AgentState) -> AgentState:
        return await preprocess_text(state)

    @with_smart_error_handling("Parse", timeout_seconds=60, enable_smart_recovery=True)
    async def wrapped_parse(state: AgentState) -> AgentState:
        return await parse_intents(state, db)

    @with_smart_error_handling(
        "Context", timeout_seconds=45, enable_smart_recovery=True
    )
    async def wrapped_context(state: AgentState) -> AgentState:
        return await get_context(state, db)

    @with_smart_error_handling("Plan", timeout_seconds=90, enable_smart_recovery=True)
    async def wrapped_plan(state: AgentState) -> AgentState:
        return await generate_plan(state)

    @with_smart_error_handling("Risk", timeout_seconds=30, enable_smart_recovery=True)
    async def wrapped_risk(state: AgentState) -> AgentState:
        return await assess_risk(state, db)

    @with_smart_error_handling(
        "Execute", timeout_seconds=120, enable_smart_recovery=True
    )
    async def wrapped_execute(state: AgentState) -> AgentState:
        return await execute_plan(state, db)

    @with_smart_error_handling(
        "AnalyzeError", timeout_seconds=30, enable_smart_recovery=True
    )
    async def wrapped_analyze_error(state: AgentState) -> AgentState:
        return await analyze_error(state)

    @with_smart_error_handling(
        "UserConfirm", timeout_seconds=300, enable_smart_recovery=True
    )
    async def wrapped_user_confirm(state: AgentState) -> AgentState:
        return await request_user_confirmation(state, db)

    # 包装函数以适配节点函数期望的AgentState对象
    def create_node_wrapper(wrapped_func):
        async def node_wrapper(state_dict: AgentStateDict) -> AgentStateDict:
            # 将TypedDict转换为AgentState对象
            agent_state = AgentState.from_dict(state_dict)

            # 调用实际的节点函数
            result_state = await wrapped_func(agent_state)

            # 将结果转换回TypedDict
            return result_state.to_dict()

        return node_wrapper

    # 添加节点（状态转换函数）
    graph.add_node("Preprocess", create_node_wrapper(wrapped_preprocess))
    graph.add_node("Parse", create_node_wrapper(wrapped_parse))
    graph.add_node("Context", create_node_wrapper(wrapped_context))
    graph.add_node("Plan", create_node_wrapper(wrapped_plan))
    graph.add_node("Risk", create_node_wrapper(wrapped_risk))
    graph.add_node("Execute", create_node_wrapper(wrapped_execute))
    graph.add_node("AnalyzeError", create_node_wrapper(wrapped_analyze_error))
    graph.add_node("UserConfirm", create_node_wrapper(wrapped_user_confirm))

    # 添加终态节点
    def success_node(state_dict: AgentStateDict) -> AgentStateDict:
        state_dict["status"] = "completed"
        return state_dict

    def failure_node(state_dict: AgentStateDict) -> AgentStateDict:
        state_dict["status"] = "failed"
        return state_dict

    graph.add_node("Success", success_node)
    graph.add_node("Failure", failure_node)

    # 设置终态节点
    graph.set_finish_point("Success")
    graph.set_finish_point("Failure")

    # 定义状态转换
    graph.set_entry_point("Preprocess")

    # 从预处理到解析
    graph.add_edge("Preprocess", "Parse")

    # 包装路由函数以处理TypedDict格式
    def wrapped_route_after_parsing(state_dict: AgentStateDict) -> str:
        state = AgentState.from_dict(state_dict)
        return route_after_parsing(state)

    # 从解析到上下文/用户确认/失败
    graph.add_conditional_edges(
        "Parse",
        wrapped_route_after_parsing,
        {
            "Context": "Context",
            "UserConfirm": "UserConfirm",
            "Failure": "Failure",
        },
    )

    def wrapped_route_after_user_confirmation(
        state_dict: AgentStateDict,
    ) -> str:
        state = AgentState.from_dict(state_dict)
        return route_after_user_confirmation(state)

    # 从用户确认到上下文/失败
    graph.add_conditional_edges(
        "UserConfirm",
        wrapped_route_after_user_confirmation,
        {"Context": "Context", "Failure": "Failure"},
    )

    # 从上下文到计划
    graph.add_edge("Context", "Plan")

    # 从计划到风险评估
    graph.add_edge("Plan", "Risk")

    def wrapped_route_after_risk_assessment(state_dict: AgentStateDict) -> str:
        state = AgentState.from_dict(state_dict)
        return route_after_risk_assessment(state)

    # 从风险评估到执行/失败
    graph.add_conditional_edges(
        "Risk",
        wrapped_route_after_risk_assessment,
        {"Execute": "Execute", "Failure": "Failure"},
    )

    def wrapped_route_after_execution(state_dict: AgentStateDict) -> str:
        state = AgentState.from_dict(state_dict)
        return route_after_execution(state)

    # 从执行到成功/错误分析
    graph.add_conditional_edges(
        "Execute",
        wrapped_route_after_execution,
        {"Success": "Success", "AnalyzeError": "AnalyzeError"},
    )

    def wrapped_route_after_error_analysis(state_dict: AgentStateDict) -> str:
        state = AgentState.from_dict(state_dict)
        return route_after_error_analysis(state)

    # 从错误分析到执行/失败
    graph.add_conditional_edges(
        "AnalyzeError",
        wrapped_route_after_error_analysis,
        {"Execute": "Execute", "Failure": "Failure"},
    )

    # 编译图
    return graph.compile()


async def run_agent(
    state: AgentState,
    db: AsyncSession,
    on_node_change=None,
    resume_from: Optional[str] = None,
) -> Dict[str, Any]:
    """
    运行Agent状态图

    Args:
        state: 初始状态
        db: 数据库会话
        on_node_change: 可选的节点变更回调函数，签名为 async def(current_node, next_node, state)
        resume_from: 可选的恢复节点名称，用于从检查点恢复执行

    Returns:
        Dict[str, Any]: 最终状态
    """
    logger.info("Running Agent", task_id=str(state.task_id), user_id=state.user_id)

    # 构建图
    graph = build_agent_graph(db)

    # 创建检查点管理器
    checkpoint_manager = CheckpointManager(db)

    # 注意：LangGraph的新版本可能不支持add_listener
    # 我们将在节点执行过程中手动处理检查点保存

    # 运行图
    try:
        # 将AgentState转换为TypedDict
        state_dict = state.to_dict()

        # 使用ainvoke方法执行图
        result = await graph.ainvoke(state_dict)

        # 手动保存最终检查点
        try:
            if isinstance(result, dict) and result.get("task_id"):
                final_state = AgentState.from_dict(result)
                await checkpoint_manager.save_checkpoint(final_state, "Completed")
        except Exception as e:
            logger.error("Failed to save final checkpoint", error=str(e))

        # 返回包装的结果以保持向后兼容
        return {"state": AgentState.from_dict(result)}
    except Exception as e:
        logger.error("Agent execution failed", task_id=str(state.task_id), error=str(e))
        raise


async def resume_from_checkpoint(
    task_id: uuid.UUID, user_id: int, db: AsyncSession, on_node_change=None
) -> Dict[str, Any]:
    """
    从检查点恢复Agent执行

    Args:
        task_id: 任务ID
        user_id: 用户ID
        db: 数据库会话
        on_node_change: 可选的节点变更回调函数

    Returns:
        Dict[str, Any]: 最终状态
    """
    logger.info("Resuming Agent from checkpoint", task_id=str(task_id), user_id=user_id)

    # 创建检查点管理器
    checkpoint_manager = CheckpointManager(db)

    # 加载最新检查点
    checkpoint_data = await checkpoint_manager.load_checkpoint(task_id, user_id)

    if not checkpoint_data:
        logger.error(
            "No checkpoint found for resuming",
            task_id=str(task_id),
            user_id=user_id,
        )
        raise ValueError(f"No checkpoint found for task {task_id}")

    # 获取状态和节点名称
    state = checkpoint_data["state"]
    node_name = checkpoint_data["node_name"]

    # 从检查点恢复执行
    return await run_agent(state, db, on_node_change, resume_from=node_name)
