"""
LLM配置管理API

提供LLM配置的CRUD操作，包括创建、读取、更新、删除和测试LLM配置。
"""

import uuid
from typing import List
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update, delete
from sqlalchemy.exc import IntegrityError

from ...core.database import get_db
from ...core.models import User, LLMConfig
from ...core.schemas import (
    APIResponse,
    LLMConfigRequest,
    LLMConfigUpdateRequest,
    LLMConfigResponse,
    LLMConfigTestRequest,
    LLMConfigTestResponse,
)
from ...core.auth import get_current_user
import structlog

# 配置结构化日志
logger = structlog.get_logger()

# 创建路由器
router = APIRouter(prefix="/llm-configs", tags=["LLM配置"])


@router.get("", response_model=APIResponse[List[LLMConfigResponse]])
async def get_llm_configs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户的LLM配置列表
    """
    try:
        # 查询用户的所有LLM配置
        stmt = select(LLMConfig).where(LLMConfig.user_id == current_user.id).order_by(LLMConfig.created_at.desc())
        result = await db.execute(stmt)
        configs = result.scalars().all()

        # 转换为响应模型
        config_responses = [
            LLMConfigResponse.from_orm_with_masked_key(config) for config in configs
        ]

        await logger.ainfo(
            "获取LLM配置列表成功",
            user_id=str(current_user.id),
            config_count=len(config_responses),
        )

        return APIResponse.success_response(
            data=config_responses, message=f"成功获取 {len(config_responses)} 个LLM配置"
        )

    except Exception as e:
        await logger.aerror(
            "获取LLM配置列表失败",
            user_id=str(current_user.id),
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取LLM配置列表失败"
        )


@router.post("", response_model=APIResponse[LLMConfigResponse])
async def create_llm_config(
    request: LLMConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建新的LLM配置
    """
    try:
        # 创建时API密钥是必需的
        if not request.api_key or not request.api_key.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建LLM配置时API密钥不能为空"
            )

        # 如果设置为默认配置，需要先取消其他默认配置
        if request.is_default:
            await _clear_default_configs(current_user.id, db)

        # 创建新的LLM配置
        llm_config = LLMConfig(
            user_id=current_user.id,
            config_name=request.config_name,
            provider=request.provider.value,
            enabled=request.enabled,
            is_default=request.is_default,
            api_key=request.api_key,  # TODO: 实际应用中需要加密存储
            api_base_url=request.api_base_url,
            model_name=request.model_name,
            max_tokens=request.max_tokens,
            temperature=Decimal(str(request.temperature)),
            timeout_seconds=request.timeout_seconds,
            max_retries=request.max_retries,
        )

        db.add(llm_config)
        await db.commit()
        await db.refresh(llm_config)

        # 转换为响应模型
        config_response = LLMConfigResponse.from_orm_with_masked_key(llm_config)

        await logger.ainfo(
            "创建LLM配置成功",
            user_id=str(current_user.id),
            config_id=str(llm_config.id),
            config_name=request.config_name,
            provider=request.provider.value,
        )

        return APIResponse.success_response(
            data=config_response, message="LLM配置创建成功"
        )

    except IntegrityError as e:
        await db.rollback()
        await logger.aerror(
            "创建LLM配置失败 - 数据完整性错误",
            user_id=str(current_user.id),
            error=str(e),
            request_data=request.model_dump(exclude={"api_key"}),
        )
        
        if "unique_user_llm_config_name" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置名称已存在，请使用不同的名称"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="LLM配置数据不符合要求"
            )

    except Exception as e:
        await db.rollback()
        await logger.aerror(
            "创建LLM配置失败",
            user_id=str(current_user.id),
            error=str(e),
            request_data=request.model_dump(exclude={"api_key"}),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建LLM配置失败"
        )


@router.put("/{config_id}", response_model=APIResponse[LLMConfigResponse])
async def update_llm_config(
    config_id: str,
    request: LLMConfigUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新LLM配置
    """
    try:
        # 验证配置ID格式
        try:
            config_uuid = uuid.UUID(config_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的配置ID格式"
            )

        # 查找配置
        stmt = select(LLMConfig).where(
            and_(LLMConfig.id == config_uuid, LLMConfig.user_id == current_user.id)
        )
        result = await db.execute(stmt)
        llm_config = result.scalar_one_or_none()

        if not llm_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="LLM配置不存在"
            )

        # 如果设置为默认配置，需要先取消其他默认配置
        if request.is_default and not llm_config.is_default:
            await _clear_default_configs(current_user.id, db, exclude_id=config_uuid)

        # 更新配置
        llm_config.config_name = request.config_name
        llm_config.provider = request.provider.value
        llm_config.enabled = request.enabled
        llm_config.is_default = request.is_default
        # 只有当API密钥不为空时才更新（编辑时允许保持原有密钥）
        if request.api_key and request.api_key.strip():
            llm_config.api_key = request.api_key  # TODO: 实际应用中需要加密存储
        llm_config.api_base_url = request.api_base_url
        llm_config.model_name = request.model_name
        llm_config.max_tokens = request.max_tokens
        llm_config.temperature = Decimal(str(request.temperature))
        llm_config.timeout_seconds = request.timeout_seconds
        llm_config.max_retries = request.max_retries

        await db.commit()
        await db.refresh(llm_config)

        # 转换为响应模型
        config_response = LLMConfigResponse.from_orm_with_masked_key(llm_config)

        await logger.ainfo(
            "更新LLM配置成功",
            user_id=str(current_user.id),
            config_id=config_id,
            config_name=request.config_name,
        )

        return APIResponse.success_response(
            data=config_response, message="LLM配置更新成功"
        )

    except HTTPException:
        await db.rollback()
        raise
    except IntegrityError as e:
        await db.rollback()
        await logger.aerror(
            "更新LLM配置失败 - 数据完整性错误",
            user_id=str(current_user.id),
            config_id=config_id,
            error=str(e),
        )
        
        if "unique_user_llm_config_name" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置名称已存在，请使用不同的名称"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="LLM配置数据不符合要求"
            )
    except Exception as e:
        await db.rollback()
        await logger.aerror(
            "更新LLM配置失败",
            user_id=str(current_user.id),
            config_id=config_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新LLM配置失败"
        )


@router.delete("/{config_id}", response_model=APIResponse[dict])
async def delete_llm_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除LLM配置
    """
    try:
        # 验证配置ID格式
        try:
            config_uuid = uuid.UUID(config_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的配置ID格式"
            )

        # 查找配置
        stmt = select(LLMConfig).where(
            and_(LLMConfig.id == config_uuid, LLMConfig.user_id == current_user.id)
        )
        result = await db.execute(stmt)
        llm_config = result.scalar_one_or_none()

        if not llm_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="LLM配置不存在"
            )

        config_name = llm_config.config_name

        # 删除配置
        await db.delete(llm_config)
        await db.commit()

        await logger.ainfo(
            "删除LLM配置成功",
            user_id=str(current_user.id),
            config_id=config_id,
            config_name=config_name,
        )

        return APIResponse.success_response(
            data={"deleted_id": config_id}, message="LLM配置删除成功"
        )

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        await logger.aerror(
            "删除LLM配置失败",
            user_id=str(current_user.id),
            config_id=config_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除LLM配置失败"
        )


@router.post("/{config_id}/set-default", response_model=APIResponse[dict])
async def set_default_llm_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    设置为默认LLM配置
    """
    try:
        # 验证配置ID格式
        try:
            config_uuid = uuid.UUID(config_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的配置ID格式"
            )

        # 查找配置
        stmt = select(LLMConfig).where(
            and_(LLMConfig.id == config_uuid, LLMConfig.user_id == current_user.id)
        )
        result = await db.execute(stmt)
        llm_config = result.scalar_one_or_none()

        if not llm_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="LLM配置不存在"
            )

        # 清除其他默认配置
        await _clear_default_configs(current_user.id, db, exclude_id=config_uuid)

        # 设置为默认
        llm_config.is_default = True
        await db.commit()

        await logger.ainfo(
            "设置默认LLM配置成功",
            user_id=str(current_user.id),
            config_id=config_id,
            config_name=llm_config.config_name,
        )

        return APIResponse.success_response(
            data={"config_id": config_id}, message="默认LLM配置设置成功"
        )

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        await logger.aerror(
            "设置默认LLM配置失败",
            user_id=str(current_user.id),
            config_id=config_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置默认LLM配置失败"
        )


@router.post("/{config_id}/test", response_model=APIResponse[LLMConfigTestResponse])
async def test_llm_config(
    config_id: str,
    request: LLMConfigTestRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    测试LLM配置连接
    """
    try:
        # 验证配置ID格式
        try:
            config_uuid = uuid.UUID(config_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的配置ID格式"
            )

        # 查找配置
        stmt = select(LLMConfig).where(
            and_(LLMConfig.id == config_uuid, LLMConfig.user_id == current_user.id)
        )
        result = await db.execute(stmt)
        llm_config = result.scalar_one_or_none()

        if not llm_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="LLM配置不存在"
            )

        # TODO: 实现实际的LLM测试逻辑
        # 这里暂时返回模拟结果
        test_response = LLMConfigTestResponse(
            success=True,
            response_text="测试连接成功！这是一个模拟响应。",
            error_message=None,
            response_time_ms=150,
        )

        await logger.ainfo(
            "测试LLM配置成功",
            user_id=str(current_user.id),
            config_id=config_id,
            provider=llm_config.provider,
        )

        return APIResponse.success_response(
            data=test_response, message="LLM配置测试完成"
        )

    except HTTPException:
        raise
    except Exception as e:
        await logger.aerror(
            "测试LLM配置失败",
            user_id=str(current_user.id),
            config_id=config_id,
            error=str(e),
            exc_info=True,
        )

        test_response = LLMConfigTestResponse(
            success=False,
            response_text=None,
            error_message=f"测试失败: {str(e)}",
            response_time_ms=None,
        )

        return APIResponse.success_response(
            data=test_response, message="LLM配置测试完成"
        )


async def _clear_default_configs(user_id: uuid.UUID, db: AsyncSession, exclude_id: uuid.UUID = None):
    """清除用户的其他默认配置"""
    stmt = update(LLMConfig).where(
        and_(
            LLMConfig.user_id == user_id,
            LLMConfig.is_default == True,
            LLMConfig.id != exclude_id if exclude_id else True
        )
    ).values(is_default=False)

    await db.execute(stmt)
