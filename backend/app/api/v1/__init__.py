from fastapi import APIRouter

from .. import conditional_orders, pending_actions
from . import actions, agent, auth, configs, discord_configs, llm_configs, orders, signals, system

api_router = APIRouter(prefix="/api/v1")

# 注册各模块的路由
api_router.include_router(auth.router)
api_router.include_router(orders.router)
api_router.include_router(configs.router)
api_router.include_router(discord_configs.router)
api_router.include_router(llm_configs.router)
api_router.include_router(actions.router)
api_router.include_router(agent.router)
api_router.include_router(signals.router)
api_router.include_router(system.router)
api_router.include_router(conditional_orders.router)
api_router.include_router(pending_actions.router)
